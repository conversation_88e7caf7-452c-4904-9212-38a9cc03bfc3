# 🔐 酷烧云网络验证系统

## 📋 系统概述

本系统为雷电模拟器中控系统集成了专业的酷烧云网络验证功能，提供卡密激活和用户登录两种验证方式，确保软件的安全使用。

## ✅ 已完成功能

### 🎨 用户界面
- ✅ **无重叠UI设计**: 使用绝对定位彻底解决元素重叠问题
- ✅ **双模式切换**: 支持卡密激活和账号登录模式
- ✅ **记住功能**: 自动保存和恢复用户输入的验证信息
- ✅ **美观界面**: 现代化的UI设计，用户体验良好

### 🔧 核心功能
- ✅ **酷烧云API对接**: 完整的API接口实现
- ✅ **MD5签名验证**: 防止参数篡改和请求伪造
- ✅ **机器码绑定**: 基于硬件信息生成唯一标识
- ✅ **心跳检测**: 定期验证防止离线破解
- ✅ **异步处理**: 避免UI阻塞，提升用户体验

### 🛡️ 安全特性
- ✅ **请求签名**: 所有API请求都包含MD5签名
- ✅ **硬件绑定**: 卡密与机器码绑定，防止多机使用
- ✅ **超时处理**: 网络请求超时保护
- ✅ **错误处理**: 完善的异常处理机制

## 📁 文件结构

```
├── core/
│   ├── kushao_auth.py          # 酷烧云验证核心模块
│   ├── license_manager.py      # 许可证管理器
│   └── auth_manager.py         # 认证管理器（兼容性封装）
├── ui/
│   └── license_dialog.py       # 验证对话框UI
├── config/
│   ├── kushao_config.json      # 酷烧云配置文件
│   └── saved_credentials.json  # 保存的用户凭据
├── docs/
│   └── 酷烧云对接使用说明.md   # 详细使用说明
├── main.py                     # 主程序入口
├── setup_kushao.py            # 一键配置脚本
├── test_kushao_auth.py        # 验证系统测试脚本
└── 酷烧云对接完整指南.md       # 完整对接指南
```

## 🚀 快速开始

### 第一步：配置酷烧云参数

运行一键配置脚本：
```bash
python setup_kushao.py
```

按提示输入您的酷烧云参数：
- APP ID: 从酷烧云后台获取
- APP KEY: 从酷烧云后台获取
- APP SECRET: 可选

### 第二步：测试系统

运行测试脚本验证配置：
```bash
python test_kushao_auth.py
```

### 第三步：启动程序

运行主程序：
```bash
python main.py
```

## 🔧 配置说明

### 酷烧云配置 (config/kushao_config.json)

```json
{
    "kushao_auth": {
        "app_id": "你的APP_ID",
        "app_key": "你的APP_KEY",
        "app_secret": "",
        "api_base": "http://api.kushao.net",
        "timeout": 15,
        "heartbeat_interval": 300,
        "max_heartbeat_failures": 3,
        "enable_debug": true
    }
}
```

### 用户凭据保存 (config/saved_credentials.json)

系统会自动保存用户的验证信息：
```json
{
    "last_mode": "card",
    "remember_card": true,
    "remember_user": false,
    "card_key": "用户的卡密",
    "username": "用户名"
}
```

## 🎯 使用流程

1. **启动程序** → 显示验证对话框
2. **选择模式** → 卡密激活 或 账号登录
3. **输入信息** → 卡密 或 用户名密码
4. **点击激活** → 发送到酷烧云验证
5. **验证成功** → 启动主程序
6. **验证失败** → 显示错误信息

## 🔍 API接口

### 卡密验证
- **接口**: `POST /card/login`
- **参数**: appid, card, machine, timestamp, version, sign
- **返回**: 验证结果和用户信息

### 用户验证
- **接口**: `POST /user/login`
- **参数**: appid, username, password, machine, timestamp, version, sign
- **返回**: 验证结果和用户信息

### 心跳检测
- **接口**: `POST /heartbeat`
- **参数**: appid, machine, timestamp, sign
- **返回**: 在线状态

## 🛠️ 故障排除

### 常见问题

1. **UI重叠问题** ✅ 已解决
   - 使用绝对定位布局
   - 固定窗口大小
   - 精确控制元素位置

2. **验证失败**
   - 检查网络连接
   - 确认APP ID和APP KEY正确
   - 查看日志获取详细错误信息

3. **记住功能不工作**
   - 检查config目录权限
   - 确认配置文件可写
   - 查看日志中的保存/加载信息

### 调试模式

在配置文件中启用调试：
```json
{
    "kushao_auth": {
        "enable_debug": true
    }
}
```

## 📞 技术支持

- **酷烧云官网**: https://www.kushao.net/
- **技术文档**: 登录后台查看完整API文档
- **客服支持**: 通过官网联系客服

## 🎉 系统状态

- ✅ UI界面: 完成，无重叠问题
- ✅ 验证功能: 完成，支持双模式
- ✅ 记住功能: 完成，自动保存恢复
- ✅ 安全机制: 完成，签名+绑定+心跳
- ⚠️ 真实对接: 需要配置真实的酷烧云参数

---

**重要提醒**: 请使用 `python setup_kushao.py` 配置真实的酷烧云参数后再使用！

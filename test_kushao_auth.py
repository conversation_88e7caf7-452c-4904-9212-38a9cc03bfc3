#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 酷烧云验证测试脚本
========================================
功能描述: 测试酷烧云网络验证系统的对接功能
主要方法: test_auth_manager(), test_license_dialog()
调用关系: 独立测试脚本，用于验证验证系统是否正常工作
注意事项:
- 需要先配置config/kushao_config.json中的真实参数
- 测试前确保网络连接正常
- 可以用于调试验证流程
========================================
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_auth_manager():
    """测试认证管理器"""
    print("🔧 测试认证管理器...")
    
    try:
        from core.auth_manager import get_auth_manager
        
        # 获取认证管理器
        auth_manager = get_auth_manager("kushao")
        print(f"✅ 认证管理器创建成功")
        
        # 测试机器码生成
        machine_code = auth_manager.get_machine_code()
        print(f"🔑 机器码: {machine_code}")
        
        # 测试配置加载
        user_info = auth_manager.get_user_info()
        print(f"📋 用户信息: {user_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证管理器测试失败: {e}")
        return False

async def test_license_verification():
    """测试许可证验证"""
    print("\n🔐 测试许可证验证...")
    
    try:
        from core.license_manager import get_license_manager
        
        # 获取许可证管理器
        license_manager = get_license_manager("kushao")
        print("✅ 许可证管理器创建成功")
        
        # 测试卡密验证（使用测试卡密）
        test_card_key = "TEST-CARD-KEY-1234"
        print(f"🧪 测试卡密验证: {test_card_key}")
        
        result = await license_manager.verify_card_license_async(test_card_key)
        
        if result['success']:
            print("✅ 卡密验证成功")
            print(f"   剩余天数: {result['data'].get('remaining_days', 'N/A')}")
            print(f"   用户类型: {result['data'].get('user_type', 'N/A')}")
        else:
            print(f"⚠️ 卡密验证失败: {result['message']}")
            print("   这是正常的，因为使用的是测试卡密")
        
        return True
        
    except Exception as e:
        print(f"❌ 许可证验证测试失败: {e}")
        return False

def test_ui_dialog():
    """测试UI对话框"""
    print("\n🎨 测试UI对话框...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.license_dialog import LicenseVerificationDialog
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = LicenseVerificationDialog()
        print("✅ 许可证验证对话框创建成功")
        
        # 显示对话框（不阻塞）
        dialog.show()
        print("✅ 对话框显示成功")
        
        # 立即关闭
        dialog.close()
        
        return True
        
    except Exception as e:
        print(f"❌ UI对话框测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n📋 测试配置加载...")
    
    try:
        import json
        from pathlib import Path
        
        config_path = Path("config/kushao_config.json")
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            kushao_config = config.get('kushao_auth', {})
            app_id = kushao_config.get('app_id', 'not_set')
            app_key = kushao_config.get('app_key', 'not_set')
            
            print(f"✅ 配置文件加载成功")
            print(f"   APP ID: {app_id}")
            print(f"   APP KEY: {'*' * len(app_key) if app_key != 'not_set' else 'not_set'}")
            
            if app_id == 'your_app_id_here' or app_key == 'your_app_key_here':
                print("⚠️ 警告: 使用的是默认配置，请设置真实的酷烧云参数")
            
        else:
            print(f"⚠️ 配置文件不存在: {config_path}")
            print("   请创建配置文件并设置真实的酷烧云参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 酷烧云验证系统测试开始")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试配置加载
    results.append(("配置加载", test_config_loading()))
    
    # 2. 测试认证管理器
    results.append(("认证管理器", test_auth_manager()))
    
    # 3. 测试许可证验证
    try:
        result = asyncio.run(test_license_verification())
        results.append(("许可证验证", result))
    except Exception as e:
        print(f"❌ 许可证验证测试异常: {e}")
        results.append(("许可证验证", False))
    
    # 4. 测试UI对话框
    results.append(("UI对话框", test_ui_dialog()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！酷烧云验证系统对接成功")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")
    
    print("\n💡 使用提示:")
    print("1. 访问 https://www.kushao.net/ 注册账号")
    print("2. 创建应用获取真实的 app_id 和 app_key")
    print("3. 编辑 config/kushao_config.json 设置真实参数")
    print("4. 重新运行此测试脚本验证配置")

if __name__ == "__main__":
    main()

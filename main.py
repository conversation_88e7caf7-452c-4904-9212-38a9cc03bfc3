#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器中控系统 - 重构后的主程序入口
========================================
功能描述: 简化的应用程序入口，使用统一的重构后系统
主要方法: main(), create_components(), connect_components()
调用关系: 程序入口点，创建并协调重构后的核心组件
注意事项:
- 使用简化配置管理器（SimpleConfigManager）
- 使用统一模拟器管理器（UnifiedEmulatorManager）
- 使用简化异步桥梁（SimpleAsyncBridge）
- 使用简化日志系统（基于Python标准logging）1
========================================
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))


def setup_logging():
    """设置简化日志系统"""
    from core.logger_manager import setup_logging, log_runtime

    # 初始化简化日志系统
    setup_logging()
    log_runtime("应用程序启动", component="main")

    import logging
    return logging.getLogger(__name__)

def create_components(logger):
    """🎯 创建核心组件 - 删除重复的模拟器管理器返回"""
    from core.simple_config import get_config_manager
    from core.async_bridge import get_async_bridge

    # 🎯 创建统一配置管理器
    config_manager = get_config_manager()
    logger.info("统一配置管理器已创建")

    # 🎯 创建修复版异步桥梁（内部会创建模拟器管理器）
    bridge = get_async_bridge()
    logger.info("修复版异步桥梁已创建")

    return config_manager, bridge

def create_ui_components(config_manager, logger):
    """创建UI组件"""
    from core.logger_manager import log_runtime
    from ui.main_window_v2 import MainWindowV2
    main_window = MainWindowV2(config_manager=config_manager)
    logger.info("UI主窗口已创建")
    log_runtime("UI主窗口已创建", component="MainWindowV2")
    return main_window

def connect_components(main_window, bridge, logger):
    """🎯 连接UI和业务层组件 - 专注应用程序协调"""

    # 🎯 设置async_bridge引用，让UI可以访问
    main_window.async_bridge = bridge

    # 🎯 只负责UI和桥梁的连接，不涉及具体业务逻辑
    # 注意：batch_operation_requested信号连接在bridge.connect_task_signals_to_ui中处理，避免重复连接
    bridge.operation_completed.connect(main_window.on_operation_completed)
    bridge.operation_failed.connect(main_window.on_operation_failed)
    bridge.operation_progress.connect(main_window.on_operation_progress)

    # 🎯 通过桥梁连接所有组件，避免main.py直接操作业务层
    bridge.connect_task_signals_to_ui(main_window)
    bridge.connect_monitor_to_ui(main_window)

    # 🎯 关键修复：异步桥梁连接完成后，处理延迟的扫描请求
    if hasattr(main_window, 'process_pending_scan_requests'):
        main_window.process_pending_scan_requests()

    logger.info("UI层和业务层已连接")

def start_services(bridge, logger):
    """🎯 启动后台服务 - 通过桥梁启动，专注应用程序协调"""
    # 🎯 通过异步桥梁启动服务，不直接操作业务层
    bridge.start_background_services()
    logger.info("后台服务已启动")

def cleanup_resources(config_manager, bridge, logger):
    """清理应用程序资源 - 恢复异步桥梁版"""
    logger.info("开始清理资源...")

    # 保存配置
    try:
        config_manager.save()
        logger.info("配置已保存")
    except Exception as e:
        logger.error(f"配置保存失败: {e}")

    # 清理异步桥梁
    try:
        bridge.shutdown()
        logger.info("异步桥梁已关闭")
    except Exception as e:
        logger.error(f"异步桥梁关闭失败: {e}")

    logger.info("应用程序已退出")

# ========================================
# 🎯 主函数 - 应用程序生命周期管理
# ========================================
# 功能描述: 管理应用程序完整生命周期，从启动到关闭的所有流程
# 主要方法: main(), cleanup_resources()
# 调用关系: 程序入口点，协调所有组件的创建、连接、启动和清理
# 注意事项: 使用try-finally确保资源正确清理，支持异常处理和优雅退出
# ========================================

def verify_license_sync():
    """🎯 同步验证许可证 - 简化版本"""
    try:
        from PyQt6.QtWidgets import QMessageBox, QDialog
        from core.auth_manager import get_auth_manager
        from ui.license_dialog import LicenseVerificationDialog

        # 获取认证管理器
        auth_manager = get_auth_manager("kushao")

        # 显示验证对话框
        dialog = LicenseVerificationDialog()

        # 显示对话框并获取用户输入
        if dialog.exec() == QDialog.DialogCode.Accepted:
            verification_data = dialog.get_verification_data()

            if not verification_data:
                return False

            # 执行验证
            try:
                if verification_data['mode'] == 'card':
                    result = auth_manager.verify_card(verification_data['card_key'])
                else:
                    result = auth_manager.verify_user(
                        verification_data['username'],
                        verification_data['password']
                    )

                if result['success']:
                    QMessageBox.information(
                        None,
                        "验证成功",
                        f"激活成功！\n剩余天数: {result['data'].get('remaining_days', 'N/A')}天\n"
                        f"用户类型: {result['data'].get('user_type', 'N/A')}"
                    )
                    return True
                else:
                    QMessageBox.critical(
                        None,
                        "验证失败",
                        f"验证失败: {result['message']}\n请检查您的许可证信息"
                    )
                    return False

            except Exception as e:
                QMessageBox.critical(
                    None,
                    "验证异常",
                    f"验证过程中发生异常: {str(e)}"
                )
                return False
        else:
            return False

    except Exception as e:
        print(f"❌ 许可证验证失败: {e}")
        return False

def main():
    """🎯 主函数 - 修复版启动序列，确保正确的初始化顺序"""
    try:
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import QTimer

        # 设置日志
        logger = setup_logging()

        # 创建应用程序
        app = QApplication(sys.argv)
        logger.info("Qt应用程序已创建")

        # 🔐 许可证验证
        logger.info("开始许可证验证...")
        license_valid = verify_license_sync()

        if not license_valid:
            logger.warning("许可证验证失败，程序退出")
            QMessageBox.critical(None, "验证失败", "许可证验证失败，程序将退出")
            sys.exit(1)

        logger.info("许可证验证成功，继续启动程序")

        # 创建核心组件
        config_manager, bridge = create_components(logger)

        # 创建UI组件
        main_window = create_ui_components(config_manager, logger)

        # 🎯 显示主窗口 - 在连接组件前显示，确保UI完全准备好
        main_window.show()
        logger.info("主窗口已显示")

        # 处理待处理的事件，确保UI完全渲染
        app.processEvents()

        # 连接组件 - UI显示后再连接
        connect_components(main_window, bridge, logger)

        # 🎯 延迟启动服务 - 确保UI和信号连接完全准备好
        def delayed_start_services():
            start_services(bridge, logger)
            logger.info("延迟启动服务完成")

        # 使用QTimer延迟1秒启动监控服务
        QTimer.singleShot(1000, delayed_start_services)

        # 运行Qt事件循环
        logger.info("启动Qt事件循环")
        try:
            exit_code = app.exec()
        finally:
            cleanup_resources(config_manager, bridge, logger)

        sys.exit(exit_code)

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
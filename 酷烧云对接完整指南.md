# 🔐 酷烧云网络验证完整对接指南

## 📋 系统状态

✅ **UI界面**: 已完成，显示正常  
✅ **验证框架**: 已完成，支持卡密和用户验证  
✅ **记住功能**: 已完成，支持记住卡密和用户名  
⚠️ **API对接**: 需要真实的酷烧云参数  

## 🚀 立即开始使用

### 第一步：注册酷烧云账号

1. **访问官网**: https://www.kushao.net/
2. **注册账号**: 点击"注册"创建开发者账号
3. **登录后台**: 使用注册的账号登录管理后台

### 第二步：创建应用

1. **创建应用**: 在后台点击"创建应用"
2. **填写信息**:
   ```
   应用名称: 雷电模拟器中控系统
   应用类型: 桌面应用
   应用描述: 企业级模拟器管理平台
   ```
3. **获取密钥**: 创建成功后获取 `app_id` 和 `app_key`

### 第三步：配置参数

编辑 `config/kushao_config.json` 文件：

```json
{
    "kushao_auth": {
        "app_id": "你的真实APP_ID",
        "app_key": "你的真实APP_KEY",
        "app_secret": "",
        "api_base": "http://api.kushao.net",
        "timeout": 15
    }
}
```

### 第四步：测试验证

1. **启动程序**: `python main.py`
2. **输入测试卡密**: 在酷烧云后台生成测试卡密
3. **验证功能**: 确认验证流程正常工作

## 🔧 当前功能特性

### ✅ 已实现功能

1. **双模式验证**
   - 卡密激活模式
   - 用户名密码登录模式

2. **记住功能**
   - 记住上次使用的验证模式
   - 记住卡密（可选）
   - 记住用户名（可选）

3. **安全特性**
   - MD5签名验证
   - 机器码绑定
   - 心跳检测防破解

4. **用户体验**
   - 简洁美观的UI界面
   - 异步验证避免卡顿
   - 详细的错误提示

### 🔄 验证流程

```
用户启动程序
    ↓
显示验证对话框
    ↓
用户输入验证信息
    ↓
发送到酷烧云服务器验证
    ↓
验证成功 → 启动主程序
验证失败 → 显示错误信息
```

## 📊 API接口说明

### 卡密验证接口

**请求**: `POST http://api.kushao.net/card/login`

**参数**:
```json
{
    "appid": "你的APP_ID",
    "card": "用户输入的卡密",
    "machine": "机器码",
    "timestamp": "时间戳",
    "version": "1.0",
    "sign": "MD5签名"
}
```

**响应**:
```json
{
    "code": 200,
    "msg": "验证成功",
    "data": {
        "expire_time": "2024-12-31 23:59:59",
        "user_type": "VIP",
        "remaining_days": 30
    }
}
```

### 用户验证接口

**请求**: `POST http://api.kushao.net/user/login`

**参数**:
```json
{
    "appid": "你的APP_ID",
    "username": "用户名",
    "password": "密码",
    "machine": "机器码",
    "timestamp": "时间戳",
    "version": "1.0",
    "sign": "MD5签名"
}
```

## 🛠️ 测试和调试

### 测试脚本

运行测试脚本验证系统：

```bash
python test_kushao_auth.py
```

### 调试模式

在配置文件中启用调试：

```json
{
    "kushao_auth": {
        "enable_debug": true
    }
}
```

### 常见问题

1. **验证失败**
   - 检查网络连接
   - 确认app_id和app_key正确
   - 查看日志文件获取详细错误信息

2. **UI显示异常**
   - 确认PyQt6已正确安装
   - 检查系统字体设置
   - 尝试重新启动程序

3. **记住功能不工作**
   - 检查config目录权限
   - 确认saved_credentials.json文件可写
   - 查看日志中的保存/加载信息

## 📞 技术支持

- **酷烧云官网**: https://www.kushao.net/
- **技术文档**: 登录后台查看完整API文档
- **客服支持**: 通过官网联系客服获取技术支持

## 🎯 下一步计划

1. **获取真实参数**: 注册酷烧云账号并获取app_id和app_key
2. **生成测试卡密**: 在后台生成测试用的激活码
3. **完整测试**: 验证所有功能是否正常工作
4. **部署上线**: 配置生产环境参数

---

**重要提醒**: 当前使用的是演示配置，需要替换为真实的酷烧云参数才能正常工作！

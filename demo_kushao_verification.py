#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 酷烧云验证演示脚本
========================================
功能描述: 演示如何使用酷烧云网络验证系统
主要方法: demo_card_verification(), demo_user_verification()
调用关系: 独立演示脚本，展示验证流程
注意事项:
- 需要先配置真实的酷烧云参数
- 演示卡密和用户名密码两种验证方式
- 包含完整的错误处理和用户交互
========================================
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def demo_card_verification():
    """演示卡密验证"""
    print("🔐 酷烧云卡密验证演示")
    print("=" * 40)
    
    try:
        from core.auth_manager import get_auth_manager
        
        # 获取认证管理器
        auth_manager = get_auth_manager("kushao")
        print("✅ 认证管理器初始化成功")
        
        # 获取机器码
        machine_code = auth_manager.get_machine_code()
        print(f"🔑 当前机器码: {machine_code}")
        
        # 输入卡密
        while True:
            card_key = input("\n请输入卡密 (输入 'quit' 退出): ").strip()
            
            if card_key.lower() == 'quit':
                break
            
            if not card_key:
                print("❌ 卡密不能为空")
                continue
            
            print(f"🔍 正在验证卡密: {card_key}")
            
            # 执行验证
            result = auth_manager.verify_card(card_key)
            
            if result['success']:
                print("✅ 卡密验证成功！")
                print(f"   剩余天数: {result['data'].get('remaining_days', 'N/A')}")
                print(f"   用户类型: {result['data'].get('user_type', 'N/A')}")
                print(f"   到期时间: {result['data'].get('expire_time', 'N/A')}")
                
                # 显示用户信息
                user_info = auth_manager.get_user_info()
                if user_info:
                    print(f"   用户数据: {user_info}")
                
                break
            else:
                print(f"❌ 卡密验证失败: {result['message']}")
                
                # 常见错误提示
                if "网络" in result['message'] or "连接" in result['message']:
                    print("💡 提示: 请检查网络连接")
                elif "卡密" in result['message'] or "无效" in result['message']:
                    print("💡 提示: 请检查卡密是否正确")
                elif "机器码" in result['message']:
                    print("💡 提示: 卡密可能已绑定其他设备")
        
    except Exception as e:
        print(f"❌ 演示过程中发生异常: {e}")

def demo_user_verification():
    """演示用户名密码验证"""
    print("\n👤 酷烧云用户验证演示")
    print("=" * 40)
    
    try:
        from core.auth_manager import get_auth_manager
        
        # 获取认证管理器
        auth_manager = get_auth_manager("kushao")
        
        # 输入用户名和密码
        while True:
            username = input("\n请输入用户名 (输入 'quit' 退出): ").strip()
            
            if username.lower() == 'quit':
                break
            
            if not username:
                print("❌ 用户名不能为空")
                continue
            
            password = input("请输入密码: ").strip()
            
            if not password:
                print("❌ 密码不能为空")
                continue
            
            print(f"🔍 正在验证用户: {username}")
            
            # 执行验证
            result = auth_manager.verify_user(username, password)
            
            if result['success']:
                print("✅ 用户验证成功！")
                print(f"   剩余天数: {result['data'].get('remaining_days', 'N/A')}")
                print(f"   用户类型: {result['data'].get('user_type', 'N/A')}")
                print(f"   到期时间: {result['data'].get('expire_time', 'N/A')}")
                break
            else:
                print(f"❌ 用户验证失败: {result['message']}")
        
    except Exception as e:
        print(f"❌ 演示过程中发生异常: {e}")

def demo_ui_verification():
    """演示UI界面验证"""
    print("\n🎨 酷烧云UI界面验证演示")
    print("=" * 40)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.license_dialog import LicenseVerificationDialog
        from core.auth_manager import get_auth_manager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 获取认证管理器
        auth_manager = get_auth_manager("kushao")
        
        # 创建并显示验证对话框
        dialog = LicenseVerificationDialog()
        
        print("✅ 许可证验证对话框已创建")
        print("💡 请在弹出的对话框中输入验证信息...")
        
        # 显示对话框
        if dialog.exec() == dialog.DialogCode.Accepted:
            verification_data = dialog.get_verification_data()
            
            if verification_data:
                print(f"📋 获取到验证数据: {verification_data['mode']} 模式")
                
                # 执行验证
                if verification_data['mode'] == 'card':
                    result = auth_manager.verify_card(verification_data['card_key'])
                else:
                    result = auth_manager.verify_user(
                        verification_data['username'], 
                        verification_data['password']
                    )
                
                if result['success']:
                    print("✅ UI验证成功！")
                    print(f"   剩余天数: {result['data'].get('remaining_days', 'N/A')}")
                    print(f"   用户类型: {result['data'].get('user_type', 'N/A')}")
                else:
                    print(f"❌ UI验证失败: {result['message']}")
            else:
                print("❌ 未获取到有效的验证数据")
        else:
            print("⚠️ 用户取消了验证")
        
    except Exception as e:
        print(f"❌ UI演示过程中发生异常: {e}")

def show_config_info():
    """显示配置信息"""
    print("📋 当前配置信息")
    print("=" * 40)
    
    try:
        import json
        from pathlib import Path
        
        config_path = Path("config/kushao_config.json")
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            kushao_config = config.get('kushao_auth', {})
            
            print(f"📁 配置文件: {config_path}")
            print(f"🆔 APP ID: {kushao_config.get('app_id', 'not_set')}")
            print(f"🔑 APP KEY: {'*' * 8 if kushao_config.get('app_key') != 'your_app_key_here' else 'not_set'}")
            print(f"🌐 API地址: {kushao_config.get('api_base', 'not_set')}")
            print(f"⏱️ 超时时间: {kushao_config.get('timeout', 'not_set')}秒")
            
            if kushao_config.get('app_id') == 'your_app_id_here':
                print("\n⚠️ 警告: 当前使用的是默认配置")
                print("💡 请按以下步骤设置真实参数:")
                print("   1. 访问 https://www.kushao.net/ 注册账号")
                print("   2. 创建应用获取 app_id 和 app_key")
                print("   3. 编辑 config/kushao_config.json 文件")
                print("   4. 重新运行此演示脚本")
            else:
                print("\n✅ 配置看起来正常，可以进行验证测试")
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            print("💡 请先运行主程序生成配置文件")
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

def main():
    """主演示函数"""
    print("🚀 酷烧云网络验证系统演示")
    print("=" * 50)
    
    # 显示配置信息
    show_config_info()
    
    while True:
        print("\n📋 请选择演示模式:")
        print("1. 卡密验证演示")
        print("2. 用户名密码验证演示")
        print("3. UI界面验证演示")
        print("4. 查看配置信息")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            demo_card_verification()
        elif choice == '2':
            demo_user_verification()
        elif choice == '3':
            demo_ui_verification()
        elif choice == '4':
            show_config_info()
        elif choice == '0':
            print("👋 演示结束，感谢使用！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门的RSA解密测试
"""

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5

def test_decrypt():
    """测试RSA解密"""
    
    # 您提供的RSA私钥
    private_key_str = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    
    # 从日志中获取的最新加密数据
    encrypted_data = "O4C27JbuNIJPrBwaIACcMauGw2vDFtT03Z64l4xWjuXeiU+RIimxlE6fNIHW8sOMLWsPZzU2wd1VL5T7u0vws+9OLGyxoG6+U+T8F2h6Y6/kB0ko4Cde60ruJbM7tK1Mu8xWtT8dTQER3aUbs1juM9YieUeXP84TyDdSoIQ6tRfSgup0c8Ys//P1BcicPmvzPKOKwUVOM5yj1sydWDiZLoDj/eQggFtEVx14FHldUQ0minO+THY0OurFt35qjmimKL8uwCPBY9UZcSQoal685VwPgbaf/fn2ASA737Uro+RKwC2Ro7uJGDbJnZw2AgUQHUfPfZDoVprRFR9L9F4ZVg=="
    
    print("🔐 开始RSA解密测试")
    print(f"加密数据长度: {len(encrypted_data)}")
    print(f"加密数据: {encrypted_data[:50]}...")
    
    try:
        # 导入RSA私钥
        print("📋 导入RSA私钥...")
        private_key = RSA.import_key(private_key_str)
        cipher = PKCS1_v1_5.new(private_key)
        print("✅ RSA私钥导入成功")
        
        # Base64解码
        print("📋 Base64解码...")
        encrypted_bytes = base64.b64decode(encrypted_data)
        print(f"✅ Base64解码成功，字节长度: {len(encrypted_bytes)}")
        
        # RSA解密
        print("📋 RSA解密...")
        decrypted_bytes = cipher.decrypt(encrypted_bytes, None)
        
        if decrypted_bytes is None:
            print("❌ RSA解密失败 - 返回None")
            return False
        
        # 解码为文本
        decrypted_text = decrypted_bytes.decode('utf-8')
        print("✅ RSA解密成功!")
        print(f"🔓 解密结果: {decrypted_text}")
        
        # 分析解密结果
        if "成功" in decrypted_text or "验证通过" in decrypted_text:
            print("🎉 验证成功!")
        elif "失败" in decrypted_text or "错误" in decrypted_text:
            print("⚠️ 验证失败，但解密成功")
        else:
            print("ℹ️ 其他响应")
        
        return True
        
    except Exception as e:
        print(f"❌ 解密异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 RSA解密专项测试")
    print("=" * 50)
    
    success = test_decrypt()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 解密测试成功!")
        print("💡 RSA解密功能正常工作")
    else:
        print("❌ 解密测试失败!")
        print("💡 请检查RSA私钥或加密数据")

if __name__ == "__main__":
    main()

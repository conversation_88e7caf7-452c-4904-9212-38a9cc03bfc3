# 🔐 酷烧云网络验证对接使用说明

## 📋 概述

本文档详细说明如何在雷电模拟器中控系统中集成酷烧云网络验证系统，实现软件的许可证验证和防破解功能。

## 🚀 快速开始

### 1. 注册酷烧云账号

1. 访问酷烧云官网：https://www.kushao.net/
2. 点击"开始使用"注册开发者账号
3. 登录后台管理系统

### 2. 创建应用

1. 在后台点击"创建应用"
2. 填写应用信息：
   - 应用名称：雷电模拟器中控系统
   - 应用描述：企业级模拟器管理平台
   - 应用类型：桌面应用
3. 创建成功后获取：
   - `app_id`：应用唯一标识
   - `app_key`：应用密钥
   - `app_secret`：应用秘钥（可选）

### 3. 配置参数

编辑 `config/kushao_config.json` 文件：

```json
{
    "kushao_auth": {
        "app_id": "你的真实APP_ID",
        "app_key": "你的真实APP_KEY", 
        "app_secret": "你的真实APP_SECRET",
        "api_base": "http://api.kushao.net",
        "timeout": 10,
        "heartbeat_interval": 300,
        "max_heartbeat_failures": 3
    }
}
```

### 4. 安装依赖

```bash
pip install requests urllib3
```

## 🔧 功能特性

### ✅ 支持的验证方式

1. **卡密验证**
   - 一次性激活码验证
   - 支持时长限制
   - 机器码绑定

2. **用户名密码验证**
   - 账号密码登录
   - 支持用户分组
   - 在线状态管理

### ✅ 安全特性

1. **MD5签名验证**
   - 防止参数篡改
   - 确保请求合法性

2. **机器码绑定**
   - 基于硬件信息生成
   - 防止多机使用

3. **心跳检测**
   - 定期验证在线状态
   - 防止离线破解

4. **异步处理**
   - 避免UI阻塞
   - 提升用户体验

## 📖 API接口说明

### 卡密验证接口

**请求地址**: `POST /card/login`

**请求参数**:
```json
{
    "appid": "应用ID",
    "card": "卡密",
    "machine": "机器码",
    "timestamp": "时间戳",
    "version": "版本号",
    "sign": "MD5签名"
}
```

**响应格式**:
```json
{
    "code": 200,
    "msg": "验证成功",
    "data": {
        "expire_time": "2024-12-31 23:59:59",
        "user_type": "VIP",
        "remaining_days": 30,
        "user_data": {}
    }
}
```

### 用户验证接口

**请求地址**: `POST /user/login`

**请求参数**:
```json
{
    "appid": "应用ID",
    "username": "用户名",
    "password": "密码",
    "machine": "机器码",
    "timestamp": "时间戳",
    "version": "版本号",
    "sign": "MD5签名"
}
```

### 心跳检测接口

**请求地址**: `POST /heartbeat`

**请求参数**:
```json
{
    "appid": "应用ID",
    "machine": "机器码",
    "timestamp": "时间戳",
    "sign": "MD5签名"
}
```

## 🎯 使用示例

### 基本使用

```python
from core.license_manager import get_license_manager

# 获取许可证管理器
license_manager = get_license_manager("kushao")

# 卡密验证
result = await license_manager.verify_card_license_async("ABCD-1234-EFGH-5678")

if result['success']:
    print(f"验证成功！剩余天数: {result['data']['remaining_days']}")
else:
    print(f"验证失败: {result['message']}")

# 用户验证
result = await license_manager.verify_user_license_async("username", "password")
```

### 检查许可证状态

```python
# 检查是否已验证
if license_manager.is_valid():
    print("许可证有效")
    
    # 获取用户信息
    user_data = license_manager.get_user_data()
    print(f"剩余天数: {license_manager.get_remaining_days()}")
    print(f"用户类型: {license_manager.get_user_type()}")
    print(f"机器码: {license_manager.get_machine_code()}")
```

## 🛠️ 后台管理

### 卡密管理

1. **生成卡密**
   - 批量生成激活码
   - 设置有效期
   - 设置用户类型

2. **卡密查询**
   - 查看使用状态
   - 查看绑定机器
   - 查看使用记录

### 用户管理

1. **用户创建**
   - 添加新用户
   - 设置密码
   - 分配权限

2. **用户监控**
   - 在线状态
   - 登录记录
   - 使用统计

## ⚠️ 注意事项

### 安全建议

1. **保护密钥**
   - 不要在代码中硬编码密钥
   - 使用配置文件管理
   - 定期更换密钥

2. **网络安全**
   - 使用HTTPS传输（如果支持）
   - 验证服务器证书
   - 防止中间人攻击

3. **客户端保护**
   - 使用代码混淆
   - 添加反调试保护
   - 定期更新客户端

### 常见问题

1. **验证失败**
   - 检查网络连接
   - 确认密钥正确
   - 查看服务器状态

2. **心跳异常**
   - 检查防火墙设置
   - 确认网络稳定
   - 调整心跳间隔

3. **机器码问题**
   - 硬件变更会影响机器码
   - 虚拟机环境可能不稳定
   - 联系客服重置绑定

## 📞 技术支持

- **官网**: https://www.kushao.net/
- **文档**: 登录后台查看详细文档
- **客服**: 通过官网联系客服

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持卡密和用户验证
- 集成心跳检测
- 提供完整UI界面

---

**注意**: 请确保在生产环境中使用真实的app_id和app_key，测试时可以使用演示配置。

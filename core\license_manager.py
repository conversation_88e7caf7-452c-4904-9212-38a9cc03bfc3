#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 许可证验证管理器 - 集成酷烧云验证
========================================
功能描述: 统一管理软件许可证验证，支持多种验证平台
主要方法: verify_license(), start_heartbeat(), check_license_status()
调用关系: 主程序启动时调用，验证通过后启动心跳检测
注意事项:
- 支持卡密和用户名密码两种验证方式
- 包含心跳检测防止破解
- 异步处理避免UI阻塞
========================================
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from .kushao_auth import KuShaoAuth


class LicenseManager:
    """🎯 许可证验证管理器"""
    
    def __init__(self, platform_type: str = "kushao"):
        """
        初始化许可证管理器
        
        Args:
            platform_type: 验证平台类型 (kushao, wenxinyun, etc.)
        """
        self.platform_type = platform_type
        self.auth_client: Optional[KuShaoAuth] = None
        self.is_licensed = False
        self.user_data = {}
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化验证客户端
        self._init_auth_client()
    
    def _init_auth_client(self):
        """初始化验证客户端"""
        try:
            if self.platform_type == "kushao":
                # 从配置文件加载酷烧云配置
                config = self._load_config()
                kushao_config = config.get('kushao_auth', {})

                project_id = kushao_config.get('project_id', kushao_config.get('app_id', ''))
                rsa_public_key = kushao_config.get('rsa_public_key', '')
                rsa_private_key = kushao_config.get('rsa_private_key', '')
                api_hosts = kushao_config.get('api_hosts', [])

                if not project_id or not rsa_public_key:
                    self.logger.warning("缺少必要的配置参数，请检查config/kushao_config.json")

                self.auth_client = KuShaoAuth(
                    project_id=project_id,
                    rsa_public_key=rsa_public_key,
                    rsa_private_key=rsa_private_key,
                    api_hosts=api_hosts
                )
                self.logger.info("酷烧云验证客户端初始化完成")
            else:
                raise ValueError(f"不支持的验证平台: {self.platform_type}")

        except Exception as e:
            self.logger.error(f"验证客户端初始化失败: {e}")
            raise

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            import json
            from pathlib import Path

            config_path = Path(__file__).parent.parent / "config" / "kushao_config.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"配置文件不存在: {config_path}")
                return {}

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    async def verify_card_license_async(self, card_key: str) -> Dict[str, Any]:
        """
        异步验证卡密许可证
        
        Args:
            card_key: 卡密
            
        Returns:
            Dict: 验证结果
        """
        try:
            if not self.auth_client:
                return self._error_result("验证客户端未初始化")
            
            self.logger.info("开始卡密验证...")
            
            # 调用验证
            result = await self.auth_client.verify_card_async(card_key)
            
            if result['success']:
                self.is_licensed = True
                self.user_data = result['data']
                
                # 启动心跳检测
                await self._start_heartbeat()
                
                self.logger.info("卡密验证成功，许可证已激活")
                return result
            else:
                self.logger.error(f"卡密验证失败: {result['message']}")
                return result
                
        except Exception as e:
            self.logger.error(f"卡密验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    async def verify_user_license_async(self, username: str, password: str) -> Dict[str, Any]:
        """
        异步验证用户名密码许可证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 验证结果
        """
        try:
            if not self.auth_client:
                return self._error_result("验证客户端未初始化")
            
            self.logger.info("开始用户验证...")
            
            # 调用验证
            result = await self.auth_client.verify_user_async(username, password)
            
            if result['success']:
                self.is_licensed = True
                self.user_data = result['data']
                
                # 启动心跳检测
                await self._start_heartbeat()
                
                self.logger.info("用户验证成功，许可证已激活")
                return result
            else:
                self.logger.error(f"用户验证失败: {result['message']}")
                return result
                
        except Exception as e:
            self.logger.error(f"用户验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    async def _start_heartbeat(self):
        """启动心跳检测"""
        try:
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
            
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            self.logger.info("心跳检测已启动")
            
        except Exception as e:
            self.logger.error(f"启动心跳检测失败: {e}")
    
    async def _heartbeat_loop(self):
        """心跳检测循环"""
        heartbeat_interval = 300  # 5分钟检测一次
        failure_count = 0
        max_failures = 3
        
        while self.is_licensed:
            try:
                await asyncio.sleep(heartbeat_interval)
                
                if not self.auth_client:
                    break
                
                # 执行心跳检测
                is_alive = await self.auth_client.heartbeat_async()
                
                if is_alive:
                    failure_count = 0
                    self.logger.debug("心跳检测正常")
                else:
                    failure_count += 1
                    self.logger.warning(f"心跳检测失败 ({failure_count}/{max_failures})")
                    
                    if failure_count >= max_failures:
                        self.logger.error("心跳检测连续失败，许可证已失效")
                        self._invalidate_license()
                        break
                
            except asyncio.CancelledError:
                self.logger.info("心跳检测已取消")
                break
            except Exception as e:
                failure_count += 1
                self.logger.error(f"心跳检测异常: {e} ({failure_count}/{max_failures})")
                
                if failure_count >= max_failures:
                    self.logger.error("心跳检测异常过多，许可证已失效")
                    self._invalidate_license()
                    break
    
    def _invalidate_license(self):
        """使许可证失效"""
        self.is_licensed = False
        self.user_data = {}
        
        if self.auth_client:
            self.auth_client.logout()
        
        self.logger.warning("许可证已失效")
        
        # 这里可以发送信号通知UI更新
        # 例如: self.license_expired.emit()
    
    def _error_result(self, message: str) -> Dict[str, Any]:
        """生成错误结果"""
        return {
            'success': False,
            'message': message,
            'data': {}
        }
    
    def is_valid(self) -> bool:
        """检查许可证是否有效"""
        return self.is_licensed and (self.auth_client.is_valid() if self.auth_client else False)
    
    def get_user_data(self) -> Dict[str, Any]:
        """获取用户数据"""
        return self.user_data if self.is_licensed else {}
    
    def get_remaining_days(self) -> int:
        """获取剩余天数"""
        return self.user_data.get('remaining_days', 0) if self.is_licensed else 0
    
    def get_user_type(self) -> str:
        """获取用户类型"""
        return self.user_data.get('user_type', '') if self.is_licensed else ''
    
    def get_machine_code(self) -> str:
        """获取机器码"""
        return self.user_data.get('machine_code', '') if self.is_licensed else ''
    
    async def shutdown(self):
        """关闭许可证管理器"""
        try:
            # 取消心跳检测
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 登出
            if self.auth_client:
                self.auth_client.logout()
            
            self.is_licensed = False
            self.user_data = {}
            
            self.logger.info("许可证管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭许可证管理器失败: {e}")


# ========================================
# 🎯 全局许可证管理器实例
# ========================================

_license_manager: Optional[LicenseManager] = None

def get_license_manager(platform_type: str = "kushao") -> LicenseManager:
    """
    获取全局许可证管理器实例
    
    Args:
        platform_type: 验证平台类型
        
    Returns:
        LicenseManager: 许可证管理器实例
    """
    global _license_manager
    
    if _license_manager is None:
        _license_manager = LicenseManager(platform_type)
    
    return _license_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
RSA解密测试脚本
"""

import base64
import json
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5

# 您提供的RSA私钥
PRIVATE_KEY = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

def test_decrypt(encrypted_data):
    """测试RSA解密"""
    try:
        print(f"🔐 开始解密测试")
        print(f"加密数据长度: {len(encrypted_data)}")
        print(f"加密数据前100字符: {encrypted_data[:100]}...")
        
        # 导入RSA私钥
        private_key = RSA.import_key(PRIVATE_KEY)
        cipher = PKCS1_v1_5.new(private_key)
        
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted_data)
        print(f"Base64解码成功，字节长度: {len(encrypted_bytes)}")
        
        # RSA解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes, None)
        
        if decrypted_bytes is None:
            print("❌ RSA解密失败")
            return None
        
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"✅ RSA解密成功!")
        print(f"解密结果: {decrypted_text}")
        
        # 尝试解析JSON
        try:
            json_data = json.loads(decrypted_text)
            print(f"✅ JSON解析成功:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
            return json_data
        except json.JSONDecodeError:
            print(f"⚠️ 不是JSON格式，原始文本: {decrypted_text}")
            return decrypted_text
            
    except Exception as e:
        print(f"❌ 解密异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 RSA解密测试工具")
    print("=" * 50)
    
    # 从日志中获取的加密响应数据
    encrypted_response = "YWjMsC15cfvFZnBMoXzIU2nbmf4+3+3QToAy8otkhkXWv4xmOhEA4KNU7KaUBFADRm1HXLZUUwsBckpExGqjHhSETtdj+jmIbBwy1tInCBpt55A9KPt4PmpPuOI2mHnYPybghxNMweAjY6KZ3SzPaZecOSGnWU4Odp99Qf3YUdMpE8cfP1yet6DYQzSMlSvs1tAx3W9zeljHurOY5TC8QuxKf7IDfgz2d6DkKDTuu+rt8l6gy7oeZKtR/su5qor3wfMiX5GajpNJx2DoEiJWrW14gYMfUrbEot9njEI0qBBOTlrLVIqJgVDrIf28rfzApTLefO7hVuR+lmPRAhQwXA=="
    
    result = test_decrypt(encrypted_response)
    
    if result:
        print("\n🎉 解密测试成功!")
    else:
        print("\n❌ 解密测试失败!")
        print("\n💡 可能的原因:")
        print("1. 使用了错误的RSA私钥")
        print("2. 服务器使用了不同的加密方式")
        print("3. 数据在传输过程中被截断")

if __name__ == "__main__":
    main()

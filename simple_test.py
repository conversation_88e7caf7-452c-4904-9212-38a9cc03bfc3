#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的酷烧云验证测试
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

def test_config():
    """测试配置加载"""
    try:
        config_path = Path("config/kushao_config.json")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件加载成功")
            print(f"   项目ID: {config['kushao_auth']['project_id']}")
            return True
        else:
            print("❌ 配置文件不存在")
            return False
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_crypto():
    """测试加密库"""
    try:
        from Crypto.PublicKey import RSA
        from Crypto.Cipher import PKCS1_v1_5
        import base64
        
        print("✅ 加密库导入成功")
        
        # 测试简单的RSA操作
        private_key_str = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
        
        private_key = RSA.import_key(private_key_str)
        print("✅ RSA私钥导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 加密库测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    try:
        import requests
        
        # 测试基本的HTTP连接
        response = requests.get("http://api.kushao.net", timeout=5)
        print(f"✅ API连接测试成功: {response.status_code}")
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 酷烧云验证系统简单测试")
    print("=" * 50)
    
    tests = [
        ("配置文件", test_config),
        ("加密库", test_crypto),
        ("API连接", test_api_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 测试 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有基础测试通过！")
        print("💡 可以尝试运行完整的验证系统")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")

if __name__ == "__main__":
    main()

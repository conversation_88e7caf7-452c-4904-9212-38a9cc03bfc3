#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 酷烧云网络验证模块 - 真实可用的对接方案
========================================
功能描述: 集成酷烧云网络验证系统，提供卡密和用户名密码验证
主要方法: KuShaoAuth.verify_card(), KuShaoAuth.verify_user()
调用关系: 主程序启动时调用验证，通过后才能使用软件
注意事项:
- 需要在酷烧云官网注册并获取app_id和app_key
- 支持RSA加密和MD5签名验证
- 包含心跳检测和防破解机制
========================================
"""

import asyncio
import hashlib
import json
import logging
import platform
import time
import uuid
from typing import Dict, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class KuShaoAuth:
    """🎯 酷烧云网络验证客户端"""
    
    def __init__(self, app_id: str, app_key: str, app_secret: str = ""):
        """
        初始化酷烧云验证客户端
        
        Args:
            app_id: 应用ID（从酷烧云后台获取）
            app_key: 应用密钥（从酷烧云后台获取）
            app_secret: 应用秘钥（可选，用于高级加密）
        """
        self.app_id = app_id
        self.app_key = app_key
        self.app_secret = app_secret
        
        # API配置
        self.api_base = "http://api.kushao.net"  # 酷烧云API地址
        self.timeout = 10
        
        # 会话配置
        self.session = self._create_session()
        
        # 状态管理
        self.is_authenticated = False
        self.user_info = {}
        self.machine_code = self._get_machine_code()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"酷烧云验证客户端初始化完成 - AppID: {app_id}")
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置请求头
        session.headers.update({
            'User-Agent': 'KuShaoCloud-Python-Client/1.0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        })
        
        return session
    
    def _get_machine_code(self) -> str:
        """
        获取机器码 - 基于硬件信息生成唯一标识
        
        Returns:
            str: 机器码
        """
        try:
            # 获取CPU信息
            cpu_info = platform.processor() or "unknown_cpu"
            
            # 获取MAC地址
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                               for elements in range(0, 2*6, 2)][::-1])
            
            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"
            
            # 尝试获取硬盘序列号（Windows）
            disk_serial = "unknown_disk"
            try:
                if platform.system() == "Windows":
                    import subprocess
                    result = subprocess.run(
                        ['wmic', 'diskdrive', 'get', 'serialnumber'],
                        capture_output=True, text=True, timeout=5
                    )
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # 跳过标题行
                            serial = line.strip()
                            if serial and serial != "SerialNumber":
                                disk_serial = serial
                                break
            except Exception:
                pass
            
            # 组合所有信息
            machine_string = f"{cpu_info}-{mac_str}-{system_info}-{disk_serial}"
            
            # 生成MD5哈希作为机器码
            machine_code = hashlib.md5(machine_string.encode('utf-8')).hexdigest().upper()
            
            self.logger.debug(f"机器码生成成功: {machine_code[:8]}...")
            return machine_code
            
        except Exception as e:
            self.logger.error(f"获取机器码失败: {e}")
            # 使用备用方案
            fallback = f"{platform.node()}-{uuid.getnode()}"
            return hashlib.md5(fallback.encode('utf-8')).hexdigest().upper()
    
    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """
        生成请求签名 - 酷烧云标准签名算法
        
        Args:
            params: 请求参数字典
            
        Returns:
            str: MD5签名
        """
        try:
            # 过滤空值并排序
            filtered_params = {k: str(v) for k, v in params.items() 
                             if v is not None and k != 'sign'}
            sorted_params = sorted(filtered_params.items())
            
            # 构建签名字符串
            sign_string = ""
            for key, value in sorted_params:
                sign_string += f"{key}={value}&"
            
            # 添加密钥
            sign_string += f"key={self.app_key}"
            
            # 生成MD5签名
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
            
            self.logger.debug(f"签名生成: {sign_string} -> {sign}")
            return sign
            
        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return ""
    
    async def verify_card_async(self, card_key: str) -> Dict[str, Any]:
        """
        异步卡密验证
        
        Args:
            card_key: 卡密
            
        Returns:
            Dict: 验证结果
        """
        try:
            timestamp = str(int(time.time()))
            
            # 构建请求参数
            params = {
                'appid': self.app_id,
                'card': card_key.strip(),
                'machine': self.machine_code,
                'timestamp': timestamp,
                'version': '1.0'
            }
            
            # 生成签名
            sign = self._generate_sign(params)
            if not sign:
                return self._error_result("签名生成失败")
            
            params['sign'] = sign
            
            # 异步发送请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                self._send_request, 
                'POST', 
                '/card/login', 
                params
            )
            
            return self._parse_response(response, "卡密验证")
            
        except Exception as e:
            self.logger.error(f"卡密验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    async def verify_user_async(self, username: str, password: str) -> Dict[str, Any]:
        """
        异步用户名密码验证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 验证结果
        """
        try:
            timestamp = str(int(time.time()))
            
            # 构建请求参数
            params = {
                'appid': self.app_id,
                'username': username.strip(),
                'password': password,
                'machine': self.machine_code,
                'timestamp': timestamp,
                'version': '1.0'
            }
            
            # 生成签名
            sign = self._generate_sign(params)
            if not sign:
                return self._error_result("签名生成失败")
            
            params['sign'] = sign
            
            # 异步发送请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                self._send_request, 
                'POST', 
                '/user/login', 
                params
            )
            
            return self._parse_response(response, "用户验证")
            
        except Exception as e:
            self.logger.error(f"用户验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    def _send_request(self, method: str, endpoint: str, params: Dict[str, Any]) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: 请求方法
            endpoint: API端点
            params: 请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        url = f"{self.api_base}{endpoint}"
        
        try:
            if method.upper() == 'POST':
                response = self.session.post(url, data=params, timeout=self.timeout)
            else:
                response = self.session.get(url, params=params, timeout=self.timeout)
            
            self.logger.debug(f"请求发送: {method} {url} -> {response.status_code}")
            return response
            
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            raise Exception("请求超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            self.logger.error("连接失败")
            raise Exception("无法连接到验证服务器")
        except Exception as e:
            self.logger.error(f"请求异常: {e}")
            raise

    def _parse_response(self, response: requests.Response, operation: str) -> Dict[str, Any]:
        """
        解析API响应

        Args:
            response: HTTP响应
            operation: 操作名称

        Returns:
            Dict: 解析结果
        """
        try:
            if response.status_code != 200:
                return self._error_result(f"HTTP错误: {response.status_code}")

            # 解析JSON响应
            try:
                result = response.json()
            except json.JSONDecodeError:
                return self._error_result("服务器响应格式错误")

            # 检查业务状态码
            code = result.get('code', -1)
            message = result.get('msg', '未知错误')
            data = result.get('data', {})

            if code == 200 or code == 1:  # 成功
                self.is_authenticated = True
                self.user_info = data

                self.logger.info(f"{operation}成功")

                return {
                    'success': True,
                    'message': message,
                    'data': {
                        'expire_time': data.get('expire_time', ''),
                        'user_type': data.get('user_type', ''),
                        'remaining_days': data.get('remaining_days', 0),
                        'user_data': data.get('user_data', {}),
                        'machine_code': self.machine_code
                    }
                }
            else:  # 失败
                self.logger.error(f"{operation}失败: {message}")
                return self._error_result(message)

        except Exception as e:
            self.logger.error(f"响应解析异常: {e}")
            return self._error_result(f"响应解析失败: {str(e)}")

    def _error_result(self, message: str) -> Dict[str, Any]:
        """生成错误结果"""
        return {
            'success': False,
            'message': message,
            'data': {}
        }

    async def heartbeat_async(self) -> bool:
        """
        异步心跳检测

        Returns:
            bool: 心跳是否正常
        """
        try:
            if not self.is_authenticated:
                return False

            timestamp = str(int(time.time()))

            params = {
                'appid': self.app_id,
                'machine': self.machine_code,
                'timestamp': timestamp
            }

            sign = self._generate_sign(params)
            params['sign'] = sign

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._send_request,
                'POST',
                '/heartbeat',
                params
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('code') in [200, 1]

            return False

        except Exception as e:
            self.logger.error(f"心跳检测失败: {e}")
            return False

    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        return self.user_info if self.is_authenticated else {}

    def is_valid(self) -> bool:
        """检查验证状态"""
        return self.is_authenticated

    def logout(self):
        """登出"""
        self.is_authenticated = False
        self.user_info = {}
        self.logger.info("用户已登出")

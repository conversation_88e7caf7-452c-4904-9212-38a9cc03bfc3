#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 酷烧云网络验证模块 - 真实可用的对接方案
========================================
功能描述: 集成酷烧云网络验证系统，提供卡密和用户名密码验证
主要方法: KuShaoAuth.verify_card(), KuShaoAuth.verify_user()
调用关系: 主程序启动时调用验证，通过后才能使用软件
注意事项:
- 需要在酷烧云官网注册并获取app_id和app_key
- 支持RSA加密和MD5签名验证
- 包含心跳检测和防破解机制
========================================
"""

import asyncio
import hashlib
import json
import logging
import platform
import time
import uuid
import base64
from typing import Dict, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

try:
    from Crypto.PublicKey import RSA
    from Crypto.Cipher import PKCS1_v1_5
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False


class KuShaoAuth:
    """🎯 酷烧云网络验证客户端 - V3 API"""

    def __init__(self, project_id: str, rsa_public_key: str, rsa_private_key: str = "", api_hosts: list = None):
        """
        初始化酷烧云验证客户端

        Args:
            project_id: 项目ID
            rsa_public_key: RSA公钥
            rsa_private_key: RSA私钥（可选）
            api_hosts: API主机列表
        """
        # 首先初始化日志，确保其他方法可以使用
        self.logger = logging.getLogger(__name__)

        self.project_id = project_id
        self.rsa_public_key = rsa_public_key
        self.rsa_private_key = rsa_private_key

        # API配置
        self.api_hosts = api_hosts or [
            "http://api.kushao.net/v3/",
            "https://api.kushao.018888.xyz/v3/",
            "https://api.ks.186777.xyz/v3/"
        ]
        self.current_host_index = 0
        self.timeout = 15
        self.encoding = "utf-8"

        # 会话配置
        self.session = self._create_session()

        # 状态管理
        self.is_authenticated = False
        self.user_info = {}
        self.device_id = self._get_device_id()

        self.logger.info(f"酷烧云验证客户端初始化完成 - ProjectID: {project_id}")

    def _decrypt_rsa_response(self, encrypted_data: str) -> str:
        """
        使用RSA私钥解密服务器响应 - 支持分块解密

        Args:
            encrypted_data: Base64编码的加密数据

        Returns:
            str: 解密后的明文
        """
        try:
            if not CRYPTO_AVAILABLE:
                self.logger.error("缺少加密库，请安装: pip install pycryptodome")
                return ""

            if not self.rsa_private_key:
                self.logger.error("未配置RSA私钥，无法解密响应")
                return ""

            # 导入RSA私钥
            private_key = RSA.import_key(self.rsa_private_key)
            cipher = PKCS1_v1_5.new(private_key)

            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            self.logger.info(f"Base64解码后长度: {len(encrypted_bytes)} 字节")

            # RSA密钥长度通常是256字节（2048位），需要分块解密
            block_size = 256  # RSA 2048位密钥的块大小
            decrypted_parts = []

            # 分块解密
            for i in range(0, len(encrypted_bytes), block_size):
                block = encrypted_bytes[i:i + block_size]
                self.logger.debug(f"解密块 {i//block_size + 1}: {len(block)} 字节")

                if len(block) != block_size:
                    self.logger.warning(f"块大小不匹配: 期望{block_size}字节，实际{len(block)}字节")
                    # 如果最后一块大小不匹配，可能需要特殊处理
                    if len(block) < block_size:
                        # 尝试填充到正确大小（通常不需要，但以防万一）
                        continue

                decrypted_block = cipher.decrypt(block, None)
                if decrypted_block is None:
                    self.logger.error(f"块 {i//block_size + 1} 解密失败")
                    return ""

                decrypted_parts.append(decrypted_block)

            # 合并所有解密的块
            if not decrypted_parts:
                self.logger.error("没有成功解密的数据块")
                return ""

            decrypted_bytes = b''.join(decrypted_parts)
            decrypted_text = decrypted_bytes.decode('utf-8')

            self.logger.info(f"🔓 RSA分块解密成功: {decrypted_text}")
            print(f"🔓 RSA解密结果: {decrypted_text}")  # 临时调试输出
            return decrypted_text

        except Exception as e:
            self.logger.error(f"RSA解密异常: {e}")
            # 尝试单块解密作为备用方案
            try:
                self.logger.info("尝试单块解密作为备用方案...")
                private_key = RSA.import_key(self.rsa_private_key)
                cipher = PKCS1_v1_5.new(private_key)
                encrypted_bytes = base64.b64decode(encrypted_data)

                # 只取前256字节尝试解密
                if len(encrypted_bytes) >= 256:
                    first_block = encrypted_bytes[:256]
                    decrypted_bytes = cipher.decrypt(first_block, None)
                    if decrypted_bytes:
                        decrypted_text = decrypted_bytes.decode('utf-8')
                        self.logger.info(f"🔓 单块解密成功: {decrypted_text}")
                        return decrypted_text
            except:
                pass

            return ""
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置请求头
        session.headers.update({
            'User-Agent': 'KuShaoCloud-Python-Client/1.0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        })
        
        return session
    
    def _get_device_id(self) -> str:
        """
        获取设备ID - 基于硬件信息生成唯一标识（5-64位）

        Returns:
            str: 设备ID
        """
        try:
            # 获取CPU信息
            cpu_info = platform.processor() or "unknown_cpu"

            # 获取MAC地址
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                               for elements in range(0, 2*6, 2)][::-1])

            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"

            # 尝试获取硬盘序列号（Windows）
            disk_serial = "unknown_disk"
            try:
                if platform.system() == "Windows":
                    import subprocess
                    result = subprocess.run(
                        ['wmic', 'diskdrive', 'get', 'serialnumber'],
                        capture_output=True, text=True, timeout=5
                    )
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # 跳过标题行
                            serial = line.strip()
                            if serial and serial != "SerialNumber":
                                disk_serial = serial
                                break
            except Exception:
                pass

            # 组合所有信息
            device_string = f"{cpu_info}-{mac_str}-{system_info}-{disk_serial}"

            # 生成MD5哈希作为设备ID，并截取到合适长度（5-64位）
            device_id = hashlib.md5(device_string.encode('utf-8')).hexdigest()[:32]

            self.logger.debug(f"设备ID生成成功: {device_id[:8]}...")
            return device_id

        except Exception as e:
            self.logger.error(f"获取设备ID失败: {e}")
            # 使用备用方案
            fallback = f"{platform.node()}-{uuid.getnode()}"
            return hashlib.md5(fallback.encode('utf-8')).hexdigest()[:32]
    
    def _generate_sign(self, request_path: str, mode: int, auth_code: str, password: str, device_id: str, timestamp: int) -> str:
        """
        生成请求签名 - 酷烧云V3 API签名算法

        Args:
            request_path: 请求路径
            mode: 计费模式
            auth_code: 账号/卡号
            password: 密码
            device_id: 设备ID
            timestamp: 时间戳

        Returns:
            str: 签名
        """
        try:
            # 按照API文档要求：请求路径 + 请求参数前5项 + 签名密钥
            # 根据文档说明，RSA签名不要拼接系统公钥，这里先不添加密钥
            sign_string = f"{request_path}{mode}{auth_code}{password}{device_id}{timestamp}"

            # 使用MD5生成签名（根据后台设定，这里假设使用MD5）
            sign = hashlib.md5(sign_string.encode(self.encoding)).hexdigest()

            self.logger.debug(f"签名字符串: {sign_string}")
            self.logger.debug(f"生成签名: {sign}")
            return sign

        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return ""
    
    async def verify_card_async(self, card_key: str, mode: int = 0) -> Dict[str, Any]:
        """
        异步卡密验证 - V3 API

        Args:
            card_key: 卡密
            mode: 计费模式 (0=计时模式, 1=计次模式)

        Returns:
            Dict: 验证结果
        """
        try:
            timestamp = int(time.time())
            request_path = f"License/verify/{self.project_id}"

            # 卡密模式：密码为空值
            password = ""

            # 生成签名
            sign = self._generate_sign(request_path, mode, card_key.strip(), password, self.device_id, timestamp)
            if not sign:
                return self._error_result("签名生成失败")

            # 构建请求参数 - 严格按照API文档格式
            # 注意：API文档要求的参数名称和格式
            params = {
                'mode': mode,           # 计费模式 [计时模式 0，计次模式 1]
                'authCode': card_key.strip(),  # 账号 [卡号模式为卡号]
                'password': password,   # 密码 [卡号模式为空值]
                'deviceId': self.device_id,    # 设备 ID [允许 5-64 位]
                'timeStamp': timestamp, # 东 8 区 10 位时间戳
                'sign': sign,          # 签名
                'encoding': self.encoding  # 字符编码 (可选)
            }

            self.logger.info(f"🔧 请求参数:")
            self.logger.info(f"   mode: {mode}")
            self.logger.info(f"   authCode: {card_key.strip()}")
            self.logger.info(f"   password: '{password}'")
            self.logger.info(f"   deviceId: {self.device_id}")
            self.logger.info(f"   timeStamp: {timestamp}")
            self.logger.info(f"   sign: {sign}")
            self.logger.info(f"   encoding: {self.encoding}")
            self.logger.info(f"🔧 请求路径: {request_path}")

            # 异步发送请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._send_request,
                'POST',
                request_path,
                params
            )

            return self._parse_response(response, "卡密验证")

        except Exception as e:
            self.logger.error(f"卡密验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    async def verify_user_async(self, username: str, password: str, mode: int = 0) -> Dict[str, Any]:
        """
        异步用户名密码验证 - V3 API

        Args:
            username: 用户名
            password: 密码
            mode: 计费模式 (0=计时模式, 1=计次模式)

        Returns:
            Dict: 验证结果
        """
        try:
            timestamp = int(time.time())
            request_path = f"License/verify/{self.project_id}"

            # 生成签名
            sign = self._generate_sign(request_path, mode, username.strip(), password, self.device_id, timestamp)
            if not sign:
                return self._error_result("签名生成失败")

            # 构建请求参数
            params = {
                'mode': mode,
                'authCode': username.strip(),
                'password': password,
                'deviceId': self.device_id,
                'timeStamp': timestamp,
                'sign': sign,
                'encoding': self.encoding
            }

            # 异步发送请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._send_request,
                'POST',
                request_path,
                params
            )

            return self._parse_response(response, "用户验证")

        except Exception as e:
            self.logger.error(f"用户验证异常: {e}")
            return self._error_result(f"验证异常: {str(e)}")
    
    def _send_request(self, method: str, endpoint: str, params: Dict[str, Any]) -> requests.Response:
        """
        发送HTTP请求 - 支持多线路自动切换

        Args:
            method: 请求方法
            endpoint: API端点
            params: 请求参数

        Returns:
            requests.Response: 响应对象
        """
        last_exception = None

        # 尝试所有API线路
        for i in range(len(self.api_hosts)):
            host_index = (self.current_host_index + i) % len(self.api_hosts)
            api_host = self.api_hosts[host_index]
            url = f"{api_host}{endpoint}"

            try:
                self.logger.debug(f"尝试线路 {host_index + 1}: {api_host}")

                if method.upper() == 'POST':
                    response = self.session.post(url, data=params, timeout=self.timeout)
                else:
                    response = self.session.get(url, params=params, timeout=self.timeout)

                self.logger.info(f"请求成功: {method} {url} -> {response.status_code}")

                # 请求成功，更新当前线路
                self.current_host_index = host_index
                return response

            except requests.exceptions.Timeout:
                last_exception = Exception(f"线路 {host_index + 1} 请求超时")
                self.logger.warning(f"线路 {host_index + 1} 请求超时，尝试下一线路")
                continue
            except requests.exceptions.ConnectionError:
                last_exception = Exception(f"线路 {host_index + 1} 连接失败")
                self.logger.warning(f"线路 {host_index + 1} 连接失败，尝试下一线路")
                continue
            except Exception as e:
                last_exception = e
                self.logger.warning(f"线路 {host_index + 1} 请求异常: {e}，尝试下一线路")
                continue

        # 所有线路都失败
        self.logger.error("所有API线路都无法连接")
        if last_exception:
            raise last_exception
        else:
            raise Exception("无法连接到任何验证服务器")

    def _parse_response(self, response: requests.Response, operation: str) -> Dict[str, Any]:
        """
        解析API响应 - V3 API (简化版本)

        Args:
            response: HTTP响应
            operation: 操作名称

        Returns:
            Dict: 解析结果
        """
        try:
            if response.status_code != 200:
                return self._error_result(f"HTTP错误: {response.status_code}")

            # 获取原始响应文本
            text_response = response.text.strip()
            self.logger.info(f"服务器原始响应长度: {len(text_response)}")
            self.logger.debug(f"响应前100字符: {text_response[:100]}...")

            # 直接尝试RSA解密（因为酷烧云返回的是加密数据）
            if text_response and len(text_response) > 50:
                self.logger.info("尝试RSA解密响应...")
                decrypted_text = self._decrypt_rsa_response(text_response)

                if decrypted_text:
                    self.logger.info(f"🔓 RSA解密成功: {decrypted_text}")
                    print(f"🔓 解密结果: {decrypted_text}")

                    # 根据解密结果判断验证是否成功
                    if any(keyword in decrypted_text for keyword in ["成功", "验证通过", "ok", "success", "验证成功"]):
                        self.is_authenticated = True
                        return {
                            'success': True,
                            'message': decrypted_text,
                            'data': {
                                'expire_time': '',
                                'user_type': 'normal',
                                'remaining_days': 0,
                                'token': '',
                                'user_data': {'response': decrypted_text},
                                'device_id': self.device_id
                            }
                        }
                    else:
                        # 验证失败，但解密成功
                        return self._error_result(decrypted_text)
                else:
                    self.logger.error("RSA解密失败")
                    return self._error_result("无法解密服务器响应")
            else:
                return self._error_result(f"服务器响应异常: {text_response}")

        except Exception as e:
            self.logger.error(f"响应解析异常: {e}")
            import traceback
            traceback.print_exc()
            return self._error_result(f"响应解析失败: {str(e)}")

    def _error_result(self, message: str) -> Dict[str, Any]:
        """生成错误结果"""
        return {
            'success': False,
            'message': message,
            'data': {}
        }

    async def heartbeat_async(self) -> bool:
        """
        异步心跳检测

        Returns:
            bool: 心跳是否正常
        """
        try:
            if not self.is_authenticated:
                return False

            timestamp = str(int(time.time()))

            params = {
                'appid': self.app_id,
                'machine': self.machine_code,
                'timestamp': timestamp
            }

            sign = self._generate_sign(params)
            params['sign'] = sign

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._send_request,
                'POST',
                '/heartbeat',
                params
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('code') in [200, 1]

            return False

        except Exception as e:
            self.logger.error(f"心跳检测失败: {e}")
            return False

    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        return self.user_info if self.is_authenticated else {}

    def is_valid(self) -> bool:
        """检查验证状态"""
        return self.is_authenticated

    def logout(self):
        """登出"""
        self.is_authenticated = False
        self.user_info = {}
        self.logger.info("用户已登出")

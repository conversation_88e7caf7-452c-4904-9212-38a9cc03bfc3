#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 许可证验证对话框 - 酷烧云验证UI
========================================
功能描述: 提供用户友好的许可证验证界面，支持卡密和用户名密码验证
主要方法: exec(), get_verification_data(), show_verification_dialog()
调用关系: 主程序启动时显示，用户输入验证信息
注意事项:
- 支持两种验证模式切换
- 异步验证避免UI阻塞
- 美观的现代化界面设计
========================================
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class LicenseVerificationDialog(QDialog):
    """🎯 许可证验证对话框"""
    
    # 自定义信号
    verification_completed = pyqtSignal(dict)  # 验证完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 验证模式：'card' 或 'user'
        self.verification_mode = 'card'
        
        # 验证结果
        self.verification_result = None
        
        # 初始化UI
        self.init_ui()
        self.setup_styles()
        self.connect_signals()

        # 加载保存的凭据
        self.load_saved_credentials()

        self.logger.info("许可证验证对话框初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("软件激活 - 雷电模拟器中控系统")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.MSWindowsFixedSizeDialogHint)
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("🔐 软件激活")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("请输入有效的许可证信息以激活软件")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin-bottom: 15px;")
        main_layout.addWidget(subtitle_label)

        # 模式切换
        mode_group = QWidget()
        mode_layout = QHBoxLayout(mode_group)
        mode_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.mode_button_group = QButtonGroup()
        self.card_mode_btn = QRadioButton("卡密激活")
        self.card_mode_btn.setChecked(True)
        self.user_mode_btn = QRadioButton("账号登录")

        self.mode_button_group.addButton(self.card_mode_btn, 0)
        self.mode_button_group.addButton(self.user_mode_btn, 1)

        mode_layout.addWidget(self.card_mode_btn)
        mode_layout.addWidget(self.user_mode_btn)
        main_layout.addWidget(mode_group)

        # 输入区域容器
        self.input_container = QWidget()
        self.input_container_layout = QVBoxLayout(self.input_container)

        # 卡密输入
        self.card_input_group = QWidget()
        card_layout = QVBoxLayout(self.card_input_group)

        card_label = QLabel("激活卡密:")
        card_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")

        self.card_input = QLineEdit()
        self.card_input.setPlaceholderText("请输入激活卡密...")
        self.card_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        card_layout.addWidget(card_label)
        card_layout.addWidget(self.card_input)

        # 用户名密码输入
        self.user_input_group = QWidget()
        user_layout = QVBoxLayout(self.user_input_group)

        username_label = QLabel("用户名:")
        username_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名...")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        password_label = QLabel("密码:")
        password_label.setStyleSheet("font-weight: bold; margin-bottom: 5px; margin-top: 10px;")

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码...")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        user_layout.addWidget(username_label)
        user_layout.addWidget(self.username_input)
        user_layout.addWidget(password_label)
        user_layout.addWidget(self.password_input)

        # 添加到容器
        self.input_container_layout.addWidget(self.card_input_group)
        self.input_container_layout.addWidget(self.user_input_group)
        main_layout.addWidget(self.input_container)

        # 按钮区域
        button_group = QWidget()
        button_layout = QHBoxLayout(button_group)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verify_btn = QPushButton("🚀 立即激活")
        self.verify_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        button_layout.addWidget(self.verify_btn)
        button_layout.addSpacing(10)
        button_layout.addWidget(self.cancel_btn)
        main_layout.addWidget(button_group)

        # 信息标签
        info_label = QLabel("💡 如需获取激活码，请联系客服\n官网: www.kushao.net")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("font-size: 10px; color: #7f8c8d; margin-top: 10px;")
        main_layout.addWidget(info_label)

        # 默认显示卡密模式
        self.switch_to_card_mode()
    
    def load_saved_credentials(self):
        """加载保存的凭据"""
        try:
            import json
            from pathlib import Path

            config_path = Path("config/saved_credentials.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)

                # 恢复上次的模式
                last_mode = credentials.get('last_mode', 'card')
                if last_mode == 'user':
                    self.user_mode_btn.setChecked(True)
                    self.switch_to_user_mode()

                # 恢复卡密（如果用户选择记住）
                if credentials.get('remember_card', False):
                    saved_card = credentials.get('card_key', '')
                    if saved_card:
                        self.card_input.setText(saved_card)

                # 恢复用户名（如果用户选择记住）
                if credentials.get('remember_user', False):
                    saved_username = credentials.get('username', '')
                    if saved_username:
                        self.username_input.setText(saved_username)

                self.logger.info("已加载保存的凭据")

        except Exception as e:
            self.logger.error(f"加载保存的凭据失败: {e}")

    def save_credentials(self, verification_data: Dict[str, Any]):
        """保存凭据"""
        try:
            import json
            from pathlib import Path

            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            config_path = config_dir / "saved_credentials.json"

            credentials = {
                'last_mode': verification_data['mode'],
                'remember_card': False,  # 可以添加复选框让用户选择
                'remember_user': False,  # 可以添加复选框让用户选择
                'card_key': '',
                'username': ''
            }

            # 如果用户选择记住，则保存（这里简化为总是记住最后一次的输入）
            if verification_data['mode'] == 'card':
                credentials['remember_card'] = True
                credentials['card_key'] = verification_data.get('card_key', '')
            else:
                credentials['remember_user'] = True
                credentials['username'] = verification_data.get('username', '')

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(credentials, f, ensure_ascii=False, indent=2)

            self.logger.info("凭据已保存")

        except Exception as e:
            self.logger.error(f"保存凭据失败: {e}")
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 8px;
            }

            QRadioButton {
                font-size: 12px;
                color: #34495e;
                margin: 0 10px;
            }

            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
    
    def connect_signals(self):
        """连接信号"""
        # 模式切换
        self.card_mode_btn.toggled.connect(self.on_mode_changed)
        self.user_mode_btn.toggled.connect(self.on_mode_changed)
        
        # 按钮点击
        self.verify_btn.clicked.connect(self.on_verify_clicked)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 回车键
        self.card_input.returnPressed.connect(self.on_verify_clicked)
        self.password_input.returnPressed.connect(self.on_verify_clicked)
    
    def on_mode_changed(self):
        """模式切换处理"""
        if self.card_mode_btn.isChecked():
            self.switch_to_card_mode()
        else:
            self.switch_to_user_mode()
    
    def switch_to_card_mode(self):
        """切换到卡密模式"""
        self.verification_mode = 'card'
        self.card_input_group.setVisible(True)
        self.user_input_group.setVisible(False)
        self.card_input.setFocus()

    def switch_to_user_mode(self):
        """切换到用户模式"""
        self.verification_mode = 'user'
        self.card_input_group.setVisible(False)
        self.user_input_group.setVisible(True)
        self.username_input.setFocus()
    
    def on_verify_clicked(self):
        """验证按钮点击处理"""
        # 获取输入数据
        verification_data = self.get_verification_data()

        if not verification_data:
            return

        # 保存凭据
        self.save_credentials(verification_data)

        # 发送验证信号
        self.verification_completed.emit(verification_data)

        # 接受对话框
        self.accept()
    
    def get_verification_data(self) -> Optional[Dict[str, Any]]:
        """
        获取验证数据
        
        Returns:
            Dict: 验证数据，如果输入无效则返回None
        """
        if self.verification_mode == 'card':
            card_key = self.card_input.text().strip()
            if not card_key:
                QMessageBox.warning(self, "输入错误", "请输入激活卡密")
                self.card_input.setFocus()
                return None
            
            return {
                'mode': 'card',
                'card_key': card_key
            }
        
        else:  # user mode
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()
            
            if not username:
                QMessageBox.warning(self, "输入错误", "请输入用户名")
                self.username_input.setFocus()
                return None
            
            if not password:
                QMessageBox.warning(self, "输入错误", "请输入密码")
                self.password_input.setFocus()
                return None
            
            return {
                'mode': 'user',
                'username': username,
                'password': password
            }
    
    def clear_inputs(self):
        """清空输入"""
        self.card_input.clear()
        self.username_input.clear()
        self.password_input.clear()
    
    def set_verification_mode(self, mode: str):
        """
        设置验证模式
        
        Args:
            mode: 'card' 或 'user'
        """
        if mode == 'card':
            self.card_mode_btn.setChecked(True)
        elif mode == 'user':
            self.user_mode_btn.setChecked(True)

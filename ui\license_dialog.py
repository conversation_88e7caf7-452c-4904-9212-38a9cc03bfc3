#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 许可证验证对话框 - 酷烧云验证UI
========================================
功能描述: 提供用户友好的许可证验证界面，支持卡密和用户名密码验证
主要方法: exec(), get_verification_data(), show_verification_dialog()
调用关系: 主程序启动时显示，用户输入验证信息
注意事项:
- 支持两种验证模式切换
- 异步验证避免UI阻塞
- 美观的现代化界面设计
========================================
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class LicenseVerificationDialog(QDialog):
    """🎯 许可证验证对话框"""
    
    # 自定义信号
    verification_completed = pyqtSignal(dict)  # 验证完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 验证模式：'card' 或 'user'
        self.verification_mode = 'card'
        
        # 验证结果
        self.verification_result = None
        
        # 初始化UI
        self.init_ui()
        self.setup_styles()
        self.connect_signals()

        # 加载保存的凭据
        self.load_saved_credentials()

        self.logger.info("许可证验证对话框初始化完成")
    
    def init_ui(self):
        """初始化用户界面 - 彻底解决重叠问题"""
        self.setWindowTitle("软件激活")
        self.setFixedSize(400, 350)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.MSWindowsFixedSizeDialogHint)
        self.setModal(True)

        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
        """)

        # 创建主widget
        main_widget = QWidget(self)
        main_widget.setGeometry(0, 0, 400, 350)

        # 标题
        title_label = QLabel("软件激活", main_widget)
        title_label.setGeometry(0, 20, 400, 30)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")

        # 模式选择
        self.card_radio = QRadioButton("卡密激活", main_widget)
        self.card_radio.setGeometry(80, 70, 100, 25)
        self.card_radio.setChecked(True)

        self.user_radio = QRadioButton("账号登录", main_widget)
        self.user_radio.setGeometry(220, 70, 100, 25)

        self.mode_group = QButtonGroup()
        self.mode_group.addButton(self.card_radio, 0)
        self.mode_group.addButton(self.user_radio, 1)

        # 卡密输入区域
        self.card_widget = QWidget(main_widget)
        self.card_widget.setGeometry(30, 110, 340, 120)

        card_label = QLabel("激活卡密:", self.card_widget)
        card_label.setGeometry(0, 0, 100, 25)
        card_label.setStyleSheet("font-weight: bold;")

        self.card_input = QLineEdit(self.card_widget)
        self.card_input.setGeometry(0, 30, 340, 35)
        self.card_input.setPlaceholderText("请输入激活卡密...")
        self.card_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)

        self.remember_card_cb = QCheckBox("记住此卡密", self.card_widget)
        self.remember_card_cb.setGeometry(0, 75, 120, 25)
        self.remember_card_cb.setChecked(True)

        # 用户登录区域
        self.user_widget = QWidget(main_widget)
        self.user_widget.setGeometry(30, 110, 340, 120)
        self.user_widget.setVisible(False)

        username_label = QLabel("用户名:", self.user_widget)
        username_label.setGeometry(0, 0, 100, 25)
        username_label.setStyleSheet("font-weight: bold;")

        self.username_input = QLineEdit(self.user_widget)
        self.username_input.setGeometry(0, 30, 340, 35)
        self.username_input.setPlaceholderText("请输入用户名...")
        self.username_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)

        password_label = QLabel("密码:", self.user_widget)
        password_label.setGeometry(170, 0, 100, 25)
        password_label.setStyleSheet("font-weight: bold;")

        self.password_input = QLineEdit(self.user_widget)
        self.password_input.setGeometry(170, 30, 170, 35)
        self.password_input.setPlaceholderText("请输入密码...")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)

        self.remember_user_cb = QCheckBox("记住用户名", self.user_widget)
        self.remember_user_cb.setGeometry(0, 75, 120, 25)
        self.remember_user_cb.setChecked(True)

        # 按钮区域
        self.ok_btn = QPushButton("激活", main_widget)
        self.ok_btn.setGeometry(100, 260, 80, 40)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.cancel_btn = QPushButton("取消", main_widget)
        self.cancel_btn.setGeometry(220, 260, 80, 40)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        # 信息标签
        info_label = QLabel("如需获取激活码，请联系客服", main_widget)
        info_label.setGeometry(0, 315, 400, 20)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #888; font-size: 12px;")
    
    def load_saved_credentials(self):
        """加载保存的凭据"""
        try:
            import json
            from pathlib import Path

            config_path = Path("config/saved_credentials.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)

                # 恢复上次的模式
                last_mode = credentials.get('last_mode', 'card')
                if last_mode == 'user':
                    self.user_radio.setChecked(True)
                    self.show_user_mode()

                # 恢复卡密（如果用户选择记住）
                if credentials.get('remember_card', False):
                    saved_card = credentials.get('card_key', '')
                    if saved_card:
                        self.card_input.setText(saved_card)
                        self.remember_card_cb.setChecked(True)

                # 恢复用户名（如果用户选择记住）
                if credentials.get('remember_user', False):
                    saved_username = credentials.get('username', '')
                    if saved_username:
                        self.username_input.setText(saved_username)
                        self.remember_user_cb.setChecked(True)

                self.logger.info("已加载保存的凭据")

        except Exception as e:
            self.logger.error(f"加载保存的凭据失败: {e}")

    def save_credentials(self, verification_data: Dict[str, Any]):
        """保存凭据"""
        try:
            import json
            from pathlib import Path

            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            config_path = config_dir / "saved_credentials.json"

            credentials = {
                'last_mode': verification_data['mode'],
                'remember_card': False,
                'remember_user': False,
                'card_key': '',
                'username': ''
            }

            # 根据用户选择保存凭据
            if verification_data['mode'] == 'card' and self.remember_card_cb.isChecked():
                credentials['remember_card'] = True
                credentials['card_key'] = verification_data.get('card_key', '')
            elif verification_data['mode'] == 'user' and self.remember_user_cb.isChecked():
                credentials['remember_user'] = True
                credentials['username'] = verification_data.get('username', '')

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(credentials, f, ensure_ascii=False, indent=2)

            self.logger.info("凭据已保存")

        except Exception as e:
            self.logger.error(f"保存凭据失败: {e}")
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 8px;
            }

            QRadioButton {
                font-size: 12px;
                color: #34495e;
                margin: 0 10px;
            }

            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
    
    def connect_signals(self):
        """连接信号"""
        # 模式切换
        self.card_radio.toggled.connect(self.on_mode_changed)
        self.user_radio.toggled.connect(self.on_mode_changed)

        # 按钮点击
        self.ok_btn.clicked.connect(self.on_ok_clicked)
        self.cancel_btn.clicked.connect(self.reject)

        # 回车键
        self.card_input.returnPressed.connect(self.on_ok_clicked)
        self.password_input.returnPressed.connect(self.on_ok_clicked)

    def on_mode_changed(self):
        """模式切换处理"""
        if self.card_radio.isChecked():
            self.show_card_mode()
        else:
            self.show_user_mode()
    
    def show_card_mode(self):
        """显示卡密模式"""
        self.verification_mode = 'card'
        self.card_widget.setVisible(True)
        self.user_widget.setVisible(False)
        self.card_input.setFocus()

    def show_user_mode(self):
        """显示用户模式"""
        self.verification_mode = 'user'
        self.card_widget.setVisible(False)
        self.user_widget.setVisible(True)
        self.username_input.setFocus()
    
    def on_ok_clicked(self):
        """确定按钮点击处理"""
        # 获取输入数据
        verification_data = self.get_verification_data()

        if not verification_data:
            return

        # 保存凭据
        self.save_credentials(verification_data)

        # 发送验证信号
        self.verification_completed.emit(verification_data)

        # 接受对话框
        self.accept()
    
    def get_verification_data(self) -> Optional[Dict[str, Any]]:
        """
        获取验证数据

        Returns:
            Dict: 验证数据，如果输入无效则返回None
        """
        if self.verification_mode == 'card':
            card_key = self.card_input.text().strip()
            if not card_key:
                QMessageBox.warning(self, "输入错误", "请输入激活卡密")
                self.card_input.setFocus()
                return None

            return {
                'mode': 'card',
                'card_key': card_key
            }

        else:  # user mode
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            if not username:
                QMessageBox.warning(self, "输入错误", "请输入用户名")
                self.username_input.setFocus()
                return None

            if not password:
                QMessageBox.warning(self, "输入错误", "请输入密码")
                self.password_input.setFocus()
                return None

            return {
                'mode': 'user',
                'username': username,
                'password': password
            }
    
    def clear_inputs(self):
        """清空输入"""
        self.card_input.clear()
        self.username_input.clear()
        self.password_input.clear()

    def set_verification_mode(self, mode: str):
        """
        设置验证模式

        Args:
            mode: 'card' 或 'user'
        """
        if mode == 'card':
            self.card_radio.setChecked(True)
            self.show_card_mode()
        elif mode == 'user':
            self.user_radio.setChecked(True)
            self.show_user_mode()

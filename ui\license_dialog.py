#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 许可证验证对话框 - 酷烧云验证UI
========================================
功能描述: 提供用户友好的许可证验证界面，支持卡密和用户名密码验证
主要方法: exec(), get_verification_data(), show_verification_dialog()
调用关系: 主程序启动时显示，用户输入验证信息
注意事项:
- 支持两种验证模式切换
- 异步验证避免UI阻塞
- 美观的现代化界面设计
========================================
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class LicenseVerificationDialog(QDialog):
    """🎯 许可证验证对话框"""
    
    # 自定义信号
    verification_completed = pyqtSignal(dict)  # 验证完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 验证模式：'card' 或 'user'
        self.verification_mode = 'card'
        
        # 验证结果
        self.verification_result = None
        
        # 初始化UI
        self.init_ui()
        self.setup_styles()
        self.connect_signals()
        
        self.logger.info("许可证验证对话框初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("软件激活 - 雷电模拟器中控系统")
        self.setFixedSize(450, 350)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.MSWindowsFixedSizeDialogHint)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题区域
        self.create_title_section(main_layout)
        
        # 模式切换区域
        self.create_mode_switch_section(main_layout)
        
        # 输入区域
        self.create_input_section(main_layout)
        
        # 按钮区域
        self.create_button_section(main_layout)
        
        # 信息区域
        self.create_info_section(main_layout)
        
        # 默认显示卡密模式
        self.switch_to_card_mode()
    
    def create_title_section(self, layout: QVBoxLayout):
        """创建标题区域"""
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 主标题
        title_label = QLabel("🔐 软件激活")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 副标题
        subtitle_label = QLabel("请输入有效的许可证信息以激活软件")
        subtitle_label.setObjectName("subtitle_label")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_widget)
    
    def create_mode_switch_section(self, layout: QVBoxLayout):
        """创建模式切换区域"""
        mode_widget = QWidget()
        mode_layout = QHBoxLayout(mode_widget)
        mode_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 模式切换按钮组
        self.mode_button_group = QButtonGroup()
        
        self.card_mode_btn = QRadioButton("卡密激活")
        self.card_mode_btn.setChecked(True)
        self.card_mode_btn.setObjectName("mode_button")
        
        self.user_mode_btn = QRadioButton("账号登录")
        self.user_mode_btn.setObjectName("mode_button")
        
        self.mode_button_group.addButton(self.card_mode_btn, 0)
        self.mode_button_group.addButton(self.user_mode_btn, 1)
        
        mode_layout.addWidget(self.card_mode_btn)
        mode_layout.addWidget(self.user_mode_btn)
        
        layout.addWidget(mode_widget)
    
    def create_input_section(self, layout: QVBoxLayout):
        """创建输入区域"""
        self.input_widget = QWidget()
        self.input_layout = QVBoxLayout(self.input_widget)
        
        # 卡密输入控件
        self.card_input_widget = QWidget()
        card_layout = QVBoxLayout(self.card_input_widget)
        
        card_label = QLabel("激活卡密:")
        card_label.setObjectName("input_label")
        
        self.card_input = QLineEdit()
        self.card_input.setPlaceholderText("请输入激活卡密...")
        self.card_input.setObjectName("input_field")
        
        card_layout.addWidget(card_label)
        card_layout.addWidget(self.card_input)
        
        # 用户名密码输入控件
        self.user_input_widget = QWidget()
        user_layout = QVBoxLayout(self.user_input_widget)
        
        username_label = QLabel("用户名:")
        username_label.setObjectName("input_label")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名...")
        self.username_input.setObjectName("input_field")
        
        password_label = QLabel("密码:")
        password_label.setObjectName("input_label")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码...")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("input_field")
        
        user_layout.addWidget(username_label)
        user_layout.addWidget(self.username_input)
        user_layout.addWidget(password_label)
        user_layout.addWidget(self.password_input)
        
        # 添加到输入布局
        self.input_layout.addWidget(self.card_input_widget)
        self.input_layout.addWidget(self.user_input_widget)
        
        layout.addWidget(self.input_widget)
    
    def create_button_section(self, layout: QVBoxLayout):
        """创建按钮区域"""
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.verify_btn = QPushButton("🚀 立即激活")
        self.verify_btn.setObjectName("primary_button")
        self.verify_btn.setMinimumSize(120, 40)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setObjectName("secondary_button")
        self.cancel_btn.setMinimumSize(120, 40)
        
        button_layout.addWidget(self.verify_btn)
        button_layout.addSpacing(20)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(button_widget)
    
    def create_info_section(self, layout: QVBoxLayout):
        """创建信息区域"""
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        info_label = QLabel("💡 如需获取激活码，请联系客服")
        info_label.setObjectName("info_label")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        contact_label = QLabel("官网: www.kushao.net")
        contact_label.setObjectName("contact_label")
        contact_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        info_layout.addWidget(info_label)
        info_layout.addWidget(contact_label)
        
        layout.addWidget(info_widget)
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 10px;
            }
            
            #title_label {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
            
            #subtitle_label {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 10px;
            }
            
            #mode_button {
                font-size: 14px;
                color: #34495e;
                margin: 0 10px;
            }
            
            #mode_button::indicator {
                width: 18px;
                height: 18px;
            }
            
            #input_label {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
            
            #input_field {
                padding: 12px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                margin-bottom: 10px;
            }
            
            #input_field:focus {
                border-color: #3498db;
                outline: none;
            }
            
            #primary_button {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            
            #primary_button:hover {
                background-color: #229954;
            }
            
            #primary_button:pressed {
                background-color: #1e8449;
            }
            
            #secondary_button {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            
            #secondary_button:hover {
                background-color: #c0392b;
            }
            
            #secondary_button:pressed {
                background-color: #a93226;
            }
            
            #info_label {
                font-size: 12px;
                color: #7f8c8d;
                margin-top: 10px;
            }
            
            #contact_label {
                font-size: 12px;
                color: #3498db;
                margin-top: 5px;
            }
        """)
    
    def connect_signals(self):
        """连接信号"""
        # 模式切换
        self.card_mode_btn.toggled.connect(self.on_mode_changed)
        self.user_mode_btn.toggled.connect(self.on_mode_changed)
        
        # 按钮点击
        self.verify_btn.clicked.connect(self.on_verify_clicked)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 回车键
        self.card_input.returnPressed.connect(self.on_verify_clicked)
        self.password_input.returnPressed.connect(self.on_verify_clicked)
    
    def on_mode_changed(self):
        """模式切换处理"""
        if self.card_mode_btn.isChecked():
            self.switch_to_card_mode()
        else:
            self.switch_to_user_mode()
    
    def switch_to_card_mode(self):
        """切换到卡密模式"""
        self.verification_mode = 'card'
        self.card_input_widget.setVisible(True)
        self.user_input_widget.setVisible(False)
        self.card_input.setFocus()
    
    def switch_to_user_mode(self):
        """切换到用户模式"""
        self.verification_mode = 'user'
        self.card_input_widget.setVisible(False)
        self.user_input_widget.setVisible(True)
        self.username_input.setFocus()
    
    def on_verify_clicked(self):
        """验证按钮点击处理"""
        # 获取输入数据
        verification_data = self.get_verification_data()
        
        if not verification_data:
            return
        
        # 发送验证信号
        self.verification_completed.emit(verification_data)
        
        # 接受对话框
        self.accept()
    
    def get_verification_data(self) -> Optional[Dict[str, Any]]:
        """
        获取验证数据
        
        Returns:
            Dict: 验证数据，如果输入无效则返回None
        """
        if self.verification_mode == 'card':
            card_key = self.card_input.text().strip()
            if not card_key:
                QMessageBox.warning(self, "输入错误", "请输入激活卡密")
                self.card_input.setFocus()
                return None
            
            return {
                'mode': 'card',
                'card_key': card_key
            }
        
        else:  # user mode
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()
            
            if not username:
                QMessageBox.warning(self, "输入错误", "请输入用户名")
                self.username_input.setFocus()
                return None
            
            if not password:
                QMessageBox.warning(self, "输入错误", "请输入密码")
                self.password_input.setFocus()
                return None
            
            return {
                'mode': 'user',
                'username': username,
                'password': password
            }
    
    def clear_inputs(self):
        """清空输入"""
        self.card_input.clear()
        self.username_input.clear()
        self.password_input.clear()
    
    def set_verification_mode(self, mode: str):
        """
        设置验证模式
        
        Args:
            mode: 'card' 或 'user'
        """
        if mode == 'card':
            self.card_mode_btn.setChecked(True)
        elif mode == 'user':
            self.user_mode_btn.setChecked(True)

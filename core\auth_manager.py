#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 认证管理器 - 兼容性封装模块
========================================
功能描述: 为现有的auth_dialog_minimal.py提供兼容性支持
主要方法: get_auth_manager(), AuthManager.verify_license()
调用关系: 被ui层的认证对话框调用
注意事项:
- 这是对license_manager的兼容性封装
- 保持与原有代码的接口兼容性
- 内部使用酷烧云验证系统
========================================
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from .license_manager import get_license_manager, LicenseManager


class AuthManager:
    """🎯 认证管理器 - 兼容性封装类"""
    
    def __init__(self, platform_type: str = "kushao"):
        """
        初始化认证管理器
        
        Args:
            platform_type: 验证平台类型
        """
        self.platform_type = platform_type
        self.license_manager = get_license_manager(platform_type)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"认证管理器初始化完成 - 平台: {platform_type}")
    
    async def verify_card_async(self, card_key: str) -> Dict[str, Any]:
        """
        异步卡密验证 - 兼容性方法
        
        Args:
            card_key: 卡密
            
        Returns:
            Dict: 验证结果
        """
        try:
            return await self.license_manager.verify_card_license_async(card_key)
        except Exception as e:
            self.logger.error(f"卡密验证异常: {e}")
            return {
                'success': False,
                'message': f'验证异常: {str(e)}',
                'data': {}
            }
    
    async def verify_user_async(self, username: str, password: str) -> Dict[str, Any]:
        """
        异步用户验证 - 兼容性方法
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 验证结果
        """
        try:
            return await self.license_manager.verify_user_license_async(username, password)
        except Exception as e:
            self.logger.error(f"用户验证异常: {e}")
            return {
                'success': False,
                'message': f'验证异常: {str(e)}',
                'data': {}
            }
    
    def verify_card(self, card_key: str) -> Dict[str, Any]:
        """
        同步卡密验证 - 兼容性方法
        
        Args:
            card_key: 卡密
            
        Returns:
            Dict: 验证结果
        """
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.verify_card_async(card_key))
                return result
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"同步卡密验证异常: {e}")
            return {
                'success': False,
                'message': f'验证异常: {str(e)}',
                'data': {}
            }
    
    def verify_user(self, username: str, password: str) -> Dict[str, Any]:
        """
        同步用户验证 - 兼容性方法
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 验证结果
        """
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.verify_user_async(username, password))
                return result
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"同步用户验证异常: {e}")
            return {
                'success': False,
                'message': f'验证异常: {str(e)}',
                'data': {}
            }
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证 - 兼容性方法
        
        Returns:
            bool: 是否已认证
        """
        return self.license_manager.is_valid()
    
    def get_user_info(self) -> Dict[str, Any]:
        """
        获取用户信息 - 兼容性方法
        
        Returns:
            Dict: 用户信息
        """
        return self.license_manager.get_user_data()
    
    def get_remaining_days(self) -> int:
        """
        获取剩余天数 - 兼容性方法
        
        Returns:
            int: 剩余天数
        """
        return self.license_manager.get_remaining_days()
    
    def get_user_type(self) -> str:
        """
        获取用户类型 - 兼容性方法
        
        Returns:
            str: 用户类型
        """
        return self.license_manager.get_user_type()
    
    def get_machine_code(self) -> str:
        """
        获取机器码 - 兼容性方法
        
        Returns:
            str: 机器码
        """
        return self.license_manager.get_machine_code()
    
    async def shutdown_async(self):
        """异步关闭认证管理器"""
        try:
            await self.license_manager.shutdown()
            self.logger.info("认证管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭认证管理器失败: {e}")
    
    def shutdown(self):
        """
        同步关闭认证管理器 - 兼容性方法
        """
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.shutdown_async())
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"同步关闭认证管理器失败: {e}")


# ========================================
# 🎯 全局认证管理器实例 - 兼容性支持
# ========================================

_auth_manager: Optional[AuthManager] = None

def get_auth_manager(platform_type: str = "kushao") -> AuthManager:
    """
    获取全局认证管理器实例 - 兼容性函数
    
    Args:
        platform_type: 验证平台类型
        
    Returns:
        AuthManager: 认证管理器实例
    """
    global _auth_manager
    
    if _auth_manager is None:
        _auth_manager = AuthManager(platform_type)
    
    return _auth_manager

def reset_auth_manager():
    """重置全局认证管理器实例"""
    global _auth_manager
    
    if _auth_manager:
        _auth_manager.shutdown()
        _auth_manager = None


# ========================================
# 🎯 兼容性别名 - 支持旧版本代码
# ========================================

# 为了兼容可能存在的旧代码
AuthenticationManager = AuthManager
get_authentication_manager = get_auth_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔐 酷烧云验证系统一键配置脚本
========================================
功能描述: 帮助用户快速配置酷烧云网络验证系统
主要方法: setup_config(), test_connection(), generate_test_data()
调用关系: 独立配置脚本，用于初始化验证系统
注意事项:
- 需要用户提供真实的酷烧云参数
- 自动测试配置是否正确
- 生成测试用的卡密和用户数据
========================================
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def print_banner():
    """显示欢迎横幅"""
    print("=" * 60)
    print("🔐 酷烧云网络验证系统 - 一键配置工具")
    print("=" * 60)
    print("📋 功能说明:")
    print("   1. 配置酷烧云API参数")
    print("   2. 测试网络连接")
    print("   3. 验证配置正确性")
    print("   4. 生成测试数据")
    print("=" * 60)

def get_user_input():
    """获取用户输入的配置信息"""
    print("\n📝 请输入酷烧云配置信息:")
    print("💡 如果您还没有账号，请先访问 https://www.kushao.net/ 注册")
    
    config = {}
    
    # 获取APP ID
    while True:
        app_id = input("\n请输入您的APP ID: ").strip()
        if app_id and app_id != "请在酷烧云后台获取真实APP_ID":
            config['app_id'] = app_id
            break
        else:
            print("❌ APP ID不能为空或使用默认值，请输入真实的APP ID")
    
    # 获取APP KEY
    while True:
        app_key = input("请输入您的APP KEY: ").strip()
        if app_key and app_key != "请在酷烧云后台获取真实APP_KEY":
            config['app_key'] = app_key
            break
        else:
            print("❌ APP KEY不能为空或使用默认值，请输入真实的APP KEY")
    
    # 获取APP SECRET（可选）
    app_secret = input("请输入您的APP SECRET (可选，直接回车跳过): ").strip()
    config['app_secret'] = app_secret
    
    # 确认信息
    print(f"\n📋 您输入的配置信息:")
    print(f"   APP ID: {config['app_id']}")
    print(f"   APP KEY: {'*' * len(config['app_key'])}")
    print(f"   APP SECRET: {'*' * len(config['app_secret']) if config['app_secret'] else '(未设置)'}")
    
    confirm = input("\n确认配置信息正确吗? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 配置已取消")
        return None
    
    return config

def save_config(config):
    """保存配置到文件"""
    try:
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        config_path = config_dir / "kushao_config.json"
        
        # 读取现有配置
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
        else:
            full_config = {
                "kushao_auth": {},
                "license_settings": {
                    "platform_type": "kushao",
                    "auto_heartbeat": True,
                    "remember_last_mode": True,
                    "default_mode": "card",
                    "auto_save_credentials": True
                },
                "ui_settings": {
                    "dialog_title": "软件激活",
                    "contact_info": "官网: www.kushao.net",
                    "support_message": "如需获取激活码，请联系客服",
                    "window_width": 350,
                    "window_height": 280
                }
            }
        
        # 更新酷烧云配置
        full_config["kushao_auth"].update({
            "app_id": config['app_id'],
            "app_key": config['app_key'],
            "app_secret": config['app_secret'],
            "api_base": "http://api.kushao.net",
            "timeout": 15,
            "heartbeat_interval": 300,
            "max_heartbeat_failures": 3,
            "enable_debug": True
        })
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(full_config, f, ensure_ascii=False, indent=4)
        
        print(f"✅ 配置已保存到: {config_path}")
        return True
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def test_configuration():
    """测试配置是否正确"""
    print("\n🔧 测试配置...")
    
    try:
        from core.kushao_auth import KuShaoAuth
        
        # 读取配置
        config_path = Path("config/kushao_config.json")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        kushao_config = config['kushao_auth']
        
        # 创建验证客户端
        auth_client = KuShaoAuth(
            app_id=kushao_config['app_id'],
            app_key=kushao_config['app_key'],
            app_secret=kushao_config.get('app_secret', '')
        )
        
        print("✅ 验证客户端创建成功")
        
        # 获取机器码
        machine_code = auth_client._get_machine_code()
        print(f"🔑 当前机器码: {machine_code}")
        
        print("✅ 配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 配置完成！后续步骤:")
    print("=" * 60)
    
    print("\n📋 1. 生成测试卡密:")
    print("   - 登录酷烧云后台")
    print("   - 进入卡密管理")
    print("   - 生成测试用的激活码")
    
    print("\n🧪 2. 测试验证功能:")
    print("   - 运行: python main.py")
    print("   - 输入生成的测试卡密")
    print("   - 验证激活流程")
    
    print("\n🔍 3. 查看日志:")
    print("   - 检查验证过程中的日志输出")
    print("   - 确认API调用是否正常")
    
    print("\n📞 4. 获取支持:")
    print("   - 官网: https://www.kushao.net/")
    print("   - 查看API文档和技术支持")
    
    print("\n💡 5. 常用命令:")
    print("   - 测试系统: python test_kushao_auth.py")
    print("   - 启动程序: python main.py")
    print("   - 查看配置: cat config/kushao_config.json")

def main():
    """主配置函数"""
    print_banner()
    
    try:
        # 获取用户输入
        config = get_user_input()
        if not config:
            return
        
        # 保存配置
        if not save_config(config):
            return
        
        # 测试配置
        if not test_configuration():
            print("⚠️ 配置测试失败，但配置已保存")
            print("💡 请检查网络连接和参数是否正确")
        
        # 显示后续步骤
        show_next_steps()
        
    except KeyboardInterrupt:
        print("\n\n❌ 配置已取消")
    except Exception as e:
        print(f"\n❌ 配置过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

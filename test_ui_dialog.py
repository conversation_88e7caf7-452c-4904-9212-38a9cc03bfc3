#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 UI对话框测试脚本
========================================
功能描述: 测试许可证验证对话框的显示和功能
主要方法: test_dialog_display(), test_dialog_functionality()
调用关系: 独立测试脚本，用于验证UI是否正常
注意事项:
- 测试对话框的显示效果
- 测试输入框和按钮功能
- 测试模式切换功能
========================================
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_dialog_display():
    """测试对话框显示"""
    print("🎨 测试许可证验证对话框显示")
    print("=" * 40)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.license_dialog import LicenseVerificationDialog
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = LicenseVerificationDialog()
        print("✅ 对话框创建成功")
        
        # 显示对话框
        print("💡 对话框将显示，请检查以下内容:")
        print("   1. 标题是否正确显示")
        print("   2. 输入框是否可见且可用")
        print("   3. 按钮是否正常显示")
        print("   4. 模式切换是否工作")
        print("   5. 样式是否美观")
        
        # 显示对话框并等待用户操作
        result = dialog.exec()
        
        if result == dialog.DialogCode.Accepted:
            verification_data = dialog.get_verification_data()
            print(f"✅ 用户输入了验证数据: {verification_data}")
            
            if verification_data:
                if verification_data['mode'] == 'card':
                    print(f"   卡密模式: {verification_data['card_key']}")
                else:
                    print(f"   用户模式: {verification_data['username']}")
            
            return True
        else:
            print("⚠️ 用户取消了对话框")
            return False
            
    except Exception as e:
        print(f"❌ 对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_credentials_saving():
    """测试凭据保存功能"""
    print("\n💾 测试凭据保存功能")
    print("=" * 40)
    
    try:
        import json
        from pathlib import Path
        
        config_path = Path("config/saved_credentials.json")
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                credentials = json.load(f)
            
            print("✅ 找到保存的凭据文件")
            print(f"   上次模式: {credentials.get('last_mode', 'unknown')}")
            print(f"   记住卡密: {credentials.get('remember_card', False)}")
            print(f"   记住用户: {credentials.get('remember_user', False)}")
            
            if credentials.get('remember_card') and credentials.get('card_key'):
                print(f"   保存的卡密: {credentials['card_key'][:4]}****")
            
            if credentials.get('remember_user') and credentials.get('username'):
                print(f"   保存的用户名: {credentials['username']}")
            
            return True
        else:
            print("⚠️ 未找到保存的凭据文件")
            print("   这是正常的，如果这是第一次运行")
            return True
            
    except Exception as e:
        print(f"❌ 凭据保存测试失败: {e}")
        return False

def test_mode_switching():
    """测试模式切换功能"""
    print("\n🔄 测试模式切换功能")
    print("=" * 40)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.license_dialog import LicenseVerificationDialog
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = LicenseVerificationDialog()
        
        # 测试初始状态
        print(f"✅ 初始模式: {dialog.verification_mode}")
        
        # 测试切换到用户模式
        dialog.switch_to_user_mode()
        print(f"✅ 切换到用户模式: {dialog.verification_mode}")
        
        # 测试切换回卡密模式
        dialog.switch_to_card_mode()
        print(f"✅ 切换到卡密模式: {dialog.verification_mode}")
        
        print("✅ 模式切换功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 模式切换测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 许可证验证对话框测试开始")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试模式切换（不需要显示UI）
    results.append(("模式切换", test_mode_switching()))
    
    # 2. 测试凭据保存
    results.append(("凭据保存", test_credentials_saving()))
    
    # 3. 测试对话框显示（需要用户交互）
    print("\n" + "=" * 50)
    print("⚠️ 接下来将显示对话框，请进行以下测试:")
    print("1. 检查界面是否美观整洁")
    print("2. 尝试在卡密输入框中输入测试内容")
    print("3. 切换到账号登录模式")
    print("4. 尝试在用户名和密码框中输入内容")
    print("5. 点击激活按钮或取消按钮")
    
    input("\n按回车键继续显示对话框...")
    
    results.append(("对话框显示", test_dialog_display()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！UI对话框工作正常")
    else:
        print("⚠️ 部分测试失败，请检查UI实现")
    
    print("\n💡 UI改进建议:")
    print("1. 界面应该清晰整洁，没有重叠元素")
    print("2. 输入框应该可见且可以正常输入")
    print("3. 按钮应该响应点击事件")
    print("4. 模式切换应该正确显示/隐藏对应输入框")
    print("5. 记住上次输入的内容（如果有保存）")

if __name__ == "__main__":
    main()
